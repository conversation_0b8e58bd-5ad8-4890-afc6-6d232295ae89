"""
錯誤處理和重連機制
提供穩定的錯誤處理和自動重連功能
"""
import asyncio
import time
from typing import Callable, Any, Optional
from loguru import logger
import traceback
from functools import wraps

class ErrorHandler:
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        
    def exponential_backoff(self, attempt: int) -> float:
        """指數退避算法"""
        delay = min(self.base_delay * (2 ** attempt), self.max_delay)
        return delay
        
    async def retry_async(self, func: Callable, *args, **kwargs) -> Any:
        """異步重試裝飾器"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt == self.max_retries:
                    logger.error(f"函數 {func.__name__} 重試 {self.max_retries} 次後仍然失敗")
                    break
                    
                delay = self.exponential_backoff(attempt)
                logger.warning(f"函數 {func.__name__} 執行失敗 (嘗試 {attempt + 1}/{self.max_retries + 1}): {e}")
                logger.info(f"等待 {delay:.2f} 秒後重試...")
                await asyncio.sleep(delay)
                
        raise last_exception

def async_error_handler(max_retries: int = 3, base_delay: float = 1.0):
    """異步錯誤處理裝飾器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            handler = ErrorHandler(max_retries, base_delay)
            return await handler.retry_async(func, *args, **kwargs)
        return wrapper
    return decorator

class ConnectionManager:
    def __init__(self, name: str, max_reconnect_attempts: int = 10, reconnect_delay: float = 5.0):
        self.name = name
        self.max_reconnect_attempts = max_reconnect_attempts
        self.reconnect_delay = reconnect_delay
        self.reconnect_attempts = 0
        self.is_connected = False
        self.last_error_time = 0
        self.error_count = 0
        self.connection_start_time = None
        
    async def connect(self, connect_func: Callable) -> bool:
        """執行連接"""
        try:
            logger.info(f"正在連接 {self.name}...")
            await connect_func()
            self.is_connected = True
            self.reconnect_attempts = 0
            self.error_count = 0
            self.connection_start_time = time.time()
            logger.success(f"{self.name} 連接成功")
            return True
        except Exception as e:
            logger.error(f"{self.name} 連接失敗: {e}")
            self.is_connected = False
            return False
            
    async def disconnect(self, disconnect_func: Optional[Callable] = None):
        """執行斷開連接"""
        try:
            if disconnect_func:
                await disconnect_func()
            self.is_connected = False
            logger.info(f"{self.name} 已斷開連接")
        except Exception as e:
            logger.error(f"斷開 {self.name} 連接時發生錯誤: {e}")
            
    async def reconnect(self, connect_func: Callable) -> bool:
        """執行重連"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"{self.name} 重連次數已達上限 ({self.max_reconnect_attempts})，停止重連")
            return False
            
        self.reconnect_attempts += 1
        logger.info(f"嘗試重連 {self.name} (第 {self.reconnect_attempts} 次)")
        
        # 計算重連延遲（指數退避）
        delay = min(self.reconnect_delay * (2 ** (self.reconnect_attempts - 1)), 300)  # 最大 5 分鐘
        logger.info(f"等待 {delay:.1f} 秒後重連...")
        await asyncio.sleep(delay)
        
        return await self.connect(connect_func)
        
    def record_error(self):
        """記錄錯誤"""
        self.error_count += 1
        self.last_error_time = time.time()
        self.is_connected = False
        
    def get_connection_stats(self) -> dict:
        """獲取連接統計信息"""
        uptime = time.time() - self.connection_start_time if self.connection_start_time else 0
        return {
            'name': self.name,
            'is_connected': self.is_connected,
            'reconnect_attempts': self.reconnect_attempts,
            'error_count': self.error_count,
            'uptime_seconds': uptime,
            'last_error_time': self.last_error_time
        }

class HealthChecker:
    def __init__(self, check_interval: float = 30.0):
        self.check_interval = check_interval
        self.components = {}
        self.is_running = False
        
    def register_component(self, name: str, health_check_func: Callable):
        """註冊需要健康檢查的組件"""
        self.components[name] = health_check_func
        
    async def check_health(self) -> dict:
        """執行健康檢查"""
        results = {}
        
        for name, check_func in self.components.items():
            try:
                result = await check_func()
                results[name] = {
                    'status': 'healthy' if result else 'unhealthy',
                    'checked_at': time.time()
                }
            except Exception as e:
                results[name] = {
                    'status': 'error',
                    'error': str(e),
                    'checked_at': time.time()
                }
                logger.error(f"健康檢查失敗 {name}: {e}")
                
        return results
        
    async def start_monitoring(self, alert_callback: Optional[Callable] = None):
        """開始健康監控"""
        self.is_running = True
        logger.info(f"開始健康監控，檢查間隔: {self.check_interval} 秒")
        
        while self.is_running:
            try:
                health_results = await self.check_health()
                
                # 檢查是否有不健康的組件
                unhealthy_components = [
                    name for name, result in health_results.items() 
                    if result['status'] != 'healthy'
                ]
                
                if unhealthy_components and alert_callback:
                    await alert_callback(unhealthy_components, health_results)
                    
            except Exception as e:
                logger.error(f"健康監控過程中發生錯誤: {e}")
                
            await asyncio.sleep(self.check_interval)
            
    def stop_monitoring(self):
        """停止健康監控"""
        self.is_running = False
        logger.info("健康監控已停止")

class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        
    def call(self, func: Callable):
        """斷路器調用"""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                logger.info("斷路器進入半開狀態")
            else:
                raise Exception("斷路器開啟，拒絕調用")
                
        try:
            result = func()
            
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
                logger.info("斷路器恢復正常")
                
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                logger.warning(f"斷路器開啟，失敗次數: {self.failure_count}")
                
            raise e
