"""
Hyperliquid 錢包監聽系統配置文件
"""
import os
from typing import List
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

class Config:
    # Telegram 配置
    TELEGRAM_API_ID = int(os.getenv('TELEGRAM_API_ID', '0'))
    TELEGRAM_API_HASH = os.getenv('TELEGRAM_API_HASH', '')
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
    TELEGRAM_CHAT_ID = int(os.getenv('TELEGRAM_CHAT_ID', '0'))
    
    # Hyperliquid 配置
    HYPERLIQUID_API_KEY = os.getenv('HYPERLIQUID_API_KEY', '')
    HYPERLIQUID_API_SECRET = os.getenv('HYPERLIQUID_API_SECRET', '')
    WEBSOCKET_URL = os.getenv('WEBSOCKET_URL', 'wss://api.hyperliquid.xyz/ws')
    
    # 監聽的錢包地址列表
    WALLET_ADDRESSES: List[str] = os.getenv('WALLET_ADDRESSES', '').split(',')
    
    # 重連配置
    RECONNECT_DELAY = int(os.getenv('RECONNECT_DELAY', '5'))
    MAX_RECONNECT_ATTEMPTS = int(os.getenv('MAX_RECONNECT_ATTEMPTS', '10'))
    
    # 日誌配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'hyperliquid_tracker.log')
    
    # 訊息格式配置
    MESSAGE_TEMPLATE = """
🔔 **{action} 訊號**
💰 錢包: `{wallet}`
📊 幣種: **{symbol}**
📈 方向: **{side}**
💎 數量: **{size}**
💵 價格: **${price}**
⏰ 時間: {timestamp}
🔗 [查看詳情](https://app.hyperliquid.xyz/trade/{symbol})
    """
    
    @classmethod
    def validate(cls):
        """驗證必要的配置項"""
        required_fields = [
            'TELEGRAM_API_ID',
            'TELEGRAM_API_HASH', 
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHAT_ID'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not getattr(cls, field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"缺少必要配置: {', '.join(missing_fields)}")
        
        if not cls.WALLET_ADDRESSES or cls.WALLET_ADDRESSES == ['']:
            raise ValueError("請設置要監聽的錢包地址")
        
        return True
