# 🚀 快速入門指南

## 5 分鐘快速設置

### 步驟 1: 準備 Telegram Bot

1. **創建 Bot**
   - 在 Telegram 找到 @BotFather
   - 發送 `/newbot` 並按提示操作
   - 記錄 <PERSON><PERSON> (格式: `123456789:ABCdefGHI...`)

2. **獲取 API 憑證**
   - 前往 https://my.telegram.org/apps
   - 登錄並創建應用
   - 記錄 API ID (數字) 和 API Hash (字符串)

3. **設置群組**
   - 創建 Telegram 群組
   - 將 Bot 加入群組並設為管理員
   - 獲取群組 Chat ID (負數，如 `-1001234567890`)

### 步驟 2: 安裝和配置

1. **下載並安裝依賴**
   ```bash
   # Windows
   start.bat
   
   # Linux/Mac
   chmod +x start.sh
   ./start.sh
   ```

2. **或者手動安裝**
   ```bash
   pip install -r requirements.txt
   python setup.py
   ```

### 步驟 3: 配置錢包地址

編輯 `.env` 文件，添加你要監聽的錢包地址：

```env
WALLET_ADDRESSES=******************************************,******************************************
```

### 步驟 4: 測試系統

```bash
python test_system.py
```

### 步驟 5: 啟動系統

```bash
python main.py
```

## 🔧 必要配置項

在 `.env` 文件中設置以下項目：

```env
# Telegram 配置 (必須)
TELEGRAM_API_ID=12345678
TELEGRAM_API_HASH=abcdef1234567890abcdef1234567890
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=-1001234567890

# 錢包地址 (必須)
WALLET_ADDRESSES=0x123...,0x456...,0x789...
```

## 📱 獲取 Telegram Chat ID 的簡單方法

1. 將 Bot 加入群組
2. 在群組發送任意訊息
3. 訪問: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. 在返回的 JSON 中找到 `"chat":{"id":-1001234567890}`

## 🎯 系統功能

- ✅ **實時監聽**: 監聽多個錢包的開關倉活動
- ✅ **智能過濾**: 自動過濾加倉訊號
- ✅ **格式化訊息**: 美觀的 Telegram 訊息格式
- ✅ **自動重連**: 網路斷線自動重連
- ✅ **健康監控**: 系統狀態監控和告警
- ✅ **統計報告**: 每小時發送交易統計

## 📊 訊息格式範例

```
🟢 開倉

👤 錢包: 0x1234...5678
🪙 幣種: BTC
📈 做多
📊 數量: 1.5000
💵 價格: $45000.5000
💰 價值: $67,500.75
⏰ 時間: 2024-01-15 14:30:25

🔗 查看詳情
```

## 🚨 常見問題

### Q: Bot 無法發送訊息
**A**: 確認 Bot 在群組中且有發送訊息權限

### Q: 沒有收到交易通知
**A**: 檢查錢包地址格式和網路連接

### Q: 系統頻繁重連
**A**: 檢查網路穩定性和防火牆設置

## 🔄 部署選項

### 本地運行
```bash
python main.py
```

### Docker 運行
```bash
docker-compose up -d
```

### 雲端部署
- Railway: 一鍵部署
- AWS EC2: 穩定可靠
- VPS: 成本效益高

## 📞 支援

- 📖 詳細文檔: 查看 `README.md`
- 🧪 系統測試: 運行 `test_system.py`
- 🔧 配置向導: 運行 `setup.py`
- 📝 日誌查看: `hyperliquid_tracker.log`

---

**🎉 恭喜！你的 Hyperliquid 錢包監聽系統已準備就緒！**
