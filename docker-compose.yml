version: '3.8'

services:
  hyperliquid-tracker:
    build: .
    container_name: hyperliquid-tracker
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - LOG_FILE=/app/logs/hyperliquid_tracker.log
    healthcheck:
      test: ["CMD", "python", "-c", "import asyncio; from config import Config; Config.validate()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - hyperliquid-network

networks:
  hyperliquid-network:
    driver: bridge

volumes:
  logs:
  data:
