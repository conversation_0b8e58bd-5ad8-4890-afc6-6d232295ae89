"""
Telegram Bot 功能
用於發送格式化的交易訊息到指定群組
"""
import asyncio
from telethon import TelegramClient, events
from telethon.errors import FloodWaitError, ChatWriteForbiddenError
from typing import Dict, Any
from loguru import logger
import traceback

class TelegramBot:
    def __init__(self, api_id: int, api_hash: str, bot_token: str, chat_id: int):
        self.api_id = api_id
        self.api_hash = api_hash
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.client = None
        self.is_connected = False
        
    async def start(self):
        """啟動 Telegram Bot"""
        try:
            logger.info("正在啟動 Telegram Bot...")
            self.client = TelegramClient('hyperliquid_bot', self.api_id, self.api_hash)
            await self.client.start(bot_token=self.bot_token)
            self.is_connected = True
            logger.success("Telegram Bot 啟動成功")
            
            # 測試發送訊息
            await self.send_message("🚀 Hyperliquid 錢包監聽系統已啟動！")
            
        except Exception as e:
            logger.error(f"Telegram Bot 啟動失敗: {e}")
            logger.error(traceback.format_exc())
            raise
            
    async def send_message(self, message: str, parse_mode: str = 'markdown'):
        """發送訊息到指定群組"""
        if not self.is_connected or not self.client:
            logger.error("Telegram Bot 未連接")
            return False
            
        try:
            await self.client.send_message(
                self.chat_id, 
                message, 
                parse_mode=parse_mode
            )
            logger.debug(f"訊息發送成功到群組 {self.chat_id}")
            return True
            
        except FloodWaitError as e:
            logger.warning(f"發送頻率限制，需等待 {e.seconds} 秒")
            await asyncio.sleep(e.seconds)
            return await self.send_message(message, parse_mode)
            
        except ChatWriteForbiddenError:
            logger.error("Bot 沒有在群組中發送訊息的權限")
            return False
            
        except Exception as e:
            logger.error(f"發送訊息失敗: {e}")
            logger.error(traceback.format_exc())
            return False
            
    async def send_trade_alert(self, trade_info: Dict[str, Any]):
        """發送交易提醒"""
        try:
            # 格式化訊息
            message = self.format_trade_message(trade_info)
            
            # 發送訊息
            success = await self.send_message(message)
            
            if success:
                logger.info(f"交易提醒發送成功: {trade_info['wallet'][:10]}... {trade_info['symbol']} {trade_info['action']}")
            else:
                logger.error(f"交易提醒發送失敗: {trade_info}")
                
            return success
            
        except Exception as e:
            logger.error(f"發送交易提醒時發生錯誤: {e}")
            return False
            
    def format_trade_message(self, trade_info: Dict[str, Any]) -> str:
        """格式化交易訊息"""
        try:
            # 根據動作類型選擇表情符號
            action_emoji = {
                'OPEN': '🟢',
                'CLOSE': '🔴',
                'ADD': '🟡'
            }
            
            # 根據方向選擇表情符號
            side_emoji = {
                'LONG': '📈',
                'SHORT': '📉',
                'BUY': '📈',
                'SELL': '📉'
            }
            
            emoji = action_emoji.get(trade_info['action'], '⚪')
            direction_emoji = side_emoji.get(trade_info['side'], '📊')
            
            # 縮短錢包地址顯示
            wallet_short = f"{trade_info['wallet'][:6]}...{trade_info['wallet'][-4:]}"
            
            # 格式化數量和價格
            size = float(trade_info['size'])
            price = float(trade_info['price'])
            
            message = f"""
{emoji} **{trade_info['action']} 訊號**

💰 錢包: `{wallet_short}`
📊 幣種: **{trade_info['symbol']}**
{direction_emoji} 方向: **{trade_info['side']}**
💎 數量: **{size:,.4f}**
💵 價格: **${price:,.4f}**
⏰ 時間: `{trade_info['timestamp']}`

🔗 [查看 Hyperliquid](https://app.hyperliquid.xyz/trade/{trade_info['symbol']})
            """.strip()
            
            return message
            
        except Exception as e:
            logger.error(f"格式化訊息失敗: {e}")
            # 返回簡化版本
            return f"🔔 {trade_info['action']}: {trade_info['symbol']} {trade_info['side']} {trade_info['size']} @ ${trade_info['price']}"
            
    async def send_system_message(self, message: str, level: str = "INFO"):
        """發送系統訊息"""
        level_emoji = {
            "INFO": "ℹ️",
            "WARNING": "⚠️", 
            "ERROR": "❌",
            "SUCCESS": "✅"
        }
        
        emoji = level_emoji.get(level, "ℹ️")
        formatted_message = f"{emoji} **系統訊息**\n\n{message}"
        
        return await self.send_message(formatted_message)
        
    async def send_statistics(self, stats: Dict[str, Any]):
        """發送統計訊息"""
        try:
            message = f"""
📊 **今日統計**

🔢 總交易數: **{stats.get('total_trades', 0)}**
🟢 開倉次數: **{stats.get('open_trades', 0)}**
🔴 平倉次數: **{stats.get('close_trades', 0)}**
💰 活躍錢包: **{stats.get('active_wallets', 0)}**
⏰ 更新時間: `{stats.get('update_time', 'N/A')}`
            """.strip()
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"發送統計訊息失敗: {e}")
            return False
            
    async def stop(self):
        """停止 Telegram Bot"""
        try:
            if self.client and self.is_connected:
                await self.send_message("🛑 Hyperliquid 錢包監聽系統已停止")
                await self.client.disconnect()
                self.is_connected = False
                logger.info("Telegram Bot 已停止")
        except Exception as e:
            logger.error(f"停止 Telegram Bot 時發生錯誤: {e}")
            
    def is_ready(self) -> bool:
        """檢查 Bot 是否準備就緒"""
        return self.is_connected and self.client is not None
