"""
簡單的 Telegram 測試腳本
"""
import asyncio
import os

async def test_telegram():
    print("🧪 Telegram 連接測試")
    print("=" * 40)
    
    # 獲取配置
    bot_token = input("請輸入你的 Bot Token: ").strip()
    chat_id = input("請輸入你的 Chat ID: ").strip()
    string_session = input("請輸入你的 String Session (可選，按 Enter 跳過): ").strip()
    
    if not bot_token or not chat_id:
        print("❌ Bot Token 和 Chat ID 不能為空")
        return
    
    try:
        chat_id = int(chat_id)
    except ValueError:
        print("❌ Chat ID 必須是數字")
        return
    
    # 更新 .env 文件
    env_content = f"""TELEGRAM_BOT_TOKEN={bot_token}
TELEGRAM_CHAT_ID={chat_id}
TELEGRAM_STRING_SESSION={string_session}
TELEGRAM_API_ID=0
TELEGRAM_API_HASH=
WALLET_ADDRESSES=
WEBSOCKET_URL=wss://api.hyperliquid.xyz/ws
LOG_LEVEL=INFO
LOG_FILE=test.log
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ 配置已保存到 .env 文件")
    
    # 測試連接
    try:
        from telegram_bot import TelegramBot
        
        bot = TelegramBot(
            bot_token=bot_token,
            chat_id=chat_id
        )
        
        print("正在連接 Telegram...")
        await bot.start()
        
        # 發送測試訊息
        test_message = """🧪 **連接測試成功！**

✅ Telegram Bot 正常工作
📊 持倉查詢功能已測試通過
🔄 系統準備就緒

你可以運行 `python main.py` 啟動完整系統！"""
        
        success = await bot.send_message(test_message)
        
        if success:
            print("✅ 測試訊息發送成功！")
            print("🎉 Telegram 配置正確，系統準備就緒！")
        else:
            print("❌ 測試訊息發送失敗")
        
        await bot.stop()
        
    except Exception as e:
        print(f"❌ Telegram 測試失敗: {e}")

if __name__ == "__main__":
    asyncio.run(test_telegram())
