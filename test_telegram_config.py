#!/usr/bin/env python3
"""
Telegram 配置測試腳本
用於診斷 Telegram Bot Token 和 Chat ID 配置問題
"""

import os
import requests
import json
from config import Config

def test_bot_token(bot_token):
    """測試 Bot Token 是否有效"""
    print(f"🔍 測試 Bot Token...")
    
    url = f"https://api.telegram.org/bot{bot_token}/getMe"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                bot_info = result['result']
                print(f"✅ Bot Token 有效")
                print(f"   Bot 用戶名: @{bot_info['username']}")
                print(f"   Bot 名稱: {bot_info['first_name']}")
                print(f"   Bot ID: {bot_info['id']}")
                return True
            else:
                print(f"❌ Bot Token 無效: {result}")
                return False
        else:
            print(f"❌ HTTP 請求失敗: {response.status_code}")
            print(f"   響應內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試 Bot Token 時發生錯誤: {e}")
        return False

def test_chat_id(bot_token, chat_id):
    """測試 Chat ID 是否有效"""
    print(f"\n🔍 測試 Chat ID: {chat_id}")
    
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    data = {
        'chat_id': chat_id,
        'text': '🧪 這是一條測試消息，用於驗證 Chat ID 配置',
        'parse_mode': 'Markdown'
    }
    
    try:
        response = requests.post(url, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                print(f"✅ Chat ID 有效，測試消息發送成功")
                return True
            else:
                print(f"❌ Chat ID 無效: {result}")
                return False
        else:
            print(f"❌ HTTP 請求失敗: {response.status_code}")
            try:
                error_result = response.json()
                print(f"   錯誤詳情: {error_result}")
                
                # 常見錯誤解釋
                if 'description' in error_result:
                    desc = error_result['description']
                    if 'chat not found' in desc.lower():
                        print("   💡 可能原因: Chat ID 不存在或格式錯誤")
                    elif 'bot was blocked' in desc.lower():
                        print("   💡 可能原因: Bot 被用戶或群組封鎖")
                    elif 'not enough rights' in desc.lower():
                        print("   💡 可能原因: Bot 沒有發送消息的權限")
                        
            except:
                print(f"   響應內容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試 Chat ID 時發生錯誤: {e}")
        return False

def get_chat_updates(bot_token):
    """獲取最近的聊天更新，幫助找到正確的 Chat ID"""
    print(f"\n🔍 獲取最近的聊天更新...")
    
    url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok') and result.get('result'):
                updates = result['result']
                print(f"✅ 找到 {len(updates)} 個更新")
                
                chat_ids = set()
                for update in updates[-10:]:  # 只顯示最近 10 個
                    if 'message' in update:
                        chat = update['message']['chat']
                        chat_ids.add(chat['id'])
                        print(f"   Chat ID: {chat['id']}")
                        print(f"   Chat 類型: {chat['type']}")
                        if 'title' in chat:
                            print(f"   群組名稱: {chat['title']}")
                        if 'username' in chat:
                            print(f"   用戶名: @{chat['username']}")
                        print()
                
                if chat_ids:
                    print(f"💡 可用的 Chat ID: {list(chat_ids)}")
                else:
                    print("💡 沒有找到可用的 Chat ID")
                    print("   請先向 Bot 發送一條消息或將 Bot 添加到群組")
                    
            else:
                print(f"❌ 沒有找到聊天更新")
                print("   請先向 Bot 發送一條消息或將 Bot 添加到群組")
                
        else:
            print(f"❌ HTTP 請求失敗: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 獲取聊天更新時發生錯誤: {e}")

def main():
    """主函數"""
    print("🚀 Telegram 配置診斷工具")
    print("=" * 50)
    
    # 加載配置
    try:
        config = Config()
        bot_token = config.telegram_bot_token
        chat_id = config.telegram_chat_id
        
        print(f"Bot Token: {bot_token[:10]}...{bot_token[-10:] if len(bot_token) > 20 else bot_token}")
        print(f"Chat ID: {chat_id}")
        print()
        
    except Exception as e:
        print(f"❌ 加載配置失敗: {e}")
        return
    
    # 測試 Bot Token
    if not test_bot_token(bot_token):
        print("\n❌ Bot Token 測試失敗，請檢查 TELEGRAM_BOT_TOKEN 環境變數")
        return
    
    # 獲取聊天更新
    get_chat_updates(bot_token)
    
    # 測試 Chat ID
    if not test_chat_id(bot_token, chat_id):
        print(f"\n❌ Chat ID 測試失敗")
        print(f"💡 建議:")
        print(f"   1. 檢查 TELEGRAM_CHAT_ID 環境變數是否正確")
        print(f"   2. 確保 Bot 已被添加到目標群組")
        print(f"   3. 確保 Bot 有發送消息的權限")
        print(f"   4. 嘗試使用上面顯示的其他 Chat ID")
    else:
        print(f"\n✅ 所有測試通過！Telegram 配置正確")

if __name__ == "__main__":
    main()
