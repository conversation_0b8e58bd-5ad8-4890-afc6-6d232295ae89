"""
系統測試腳本
用於測試各個組件的功能
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from config import Config
from telegram_bot import TelegramBot
from message_formatter import MessageFormatter
from hyperliquid_client import HyperliquidClient

class SystemTester:
    def __init__(self):
        self.message_formatter = MessageFormatter()
        
    async def test_telegram_bot(self):
        """測試 Telegram Bot 功能"""
        print("🧪 測試 Telegram Bot...")
        
        try:
            bot = TelegramBot(
                api_id=Config.TELEGRAM_API_ID,
                api_hash=Config.TELEGRAM_API_HASH,
                bot_token=Config.TELEGRAM_BOT_TOKEN,
                chat_id=Config.TELEGRAM_CHAT_ID
            )
            
            await bot.start()
            
            # 測試發送基本訊息
            success = await bot.send_message("🧪 **測試訊息**\n\n這是一條測試訊息，用於驗證 Bot 功能正常。")
            
            if success:
                print("✅ Telegram Bot 測試通過")
            else:
                print("❌ Telegram Bot 測試失敗")
                
            await bot.stop()
            return success
            
        except Exception as e:
            print(f"❌ Telegram Bot 測試失敗: {e}")
            return False
            
    async def test_message_formatter(self):
        """測試訊息格式化功能"""
        print("🧪 測試訊息格式化...")
        
        try:
            # 模擬交易數據
            test_trade = {
                'wallet': '******************************************',
                'symbol': 'BTC',
                'action': 'OPEN',
                'side': 'LONG',
                'size': '1.5',
                'price': '45000.50',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'raw_data': {'dir': 'Open Long'}
            }
            
            # 測試驗證功能
            is_valid = self.message_formatter.validate_trade_info(test_trade)
            print(f"  - 交易信息驗證: {'✅' if is_valid else '❌'}")
            
            # 測試過濾功能
            should_filter = self.message_formatter.should_filter_message(test_trade)
            print(f"  - 過濾測試 (應該為 False): {'✅' if not should_filter else '❌'}")
            
            # 測試加倉過濾
            add_trade = test_trade.copy()
            add_trade['action'] = 'ADD'
            should_filter_add = self.message_formatter.should_filter_message(add_trade)
            print(f"  - 加倉過濾測試 (應該為 True): {'✅' if should_filter_add else '❌'}")
            
            # 測試格式化
            formatted_message = self.message_formatter.format_for_telegram(test_trade)
            print(f"  - 訊息格式化: {'✅' if formatted_message and len(formatted_message) > 0 else '❌'}")
            
            print("✅ 訊息格式化測試通過")
            return True
            
        except Exception as e:
            print(f"❌ 訊息格式化測試失敗: {e}")
            return False
            
    async def test_config_validation(self):
        """測試配置驗證"""
        print("🧪 測試配置驗證...")
        
        try:
            Config.validate()
            print("✅ 配置驗證通過")
            
            # 顯示配置信息
            print(f"  - 監聽錢包數量: {len(Config.WALLET_ADDRESSES)}")
            print(f"  - WebSocket URL: {Config.WEBSOCKET_URL}")
            print(f"  - Telegram Chat ID: {Config.TELEGRAM_CHAT_ID}")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置驗證失敗: {e}")
            return False
            
    async def test_websocket_connection(self):
        """測試 WebSocket 連接"""
        print("🧪 測試 WebSocket 連接...")
        
        try:
            client = HyperliquidClient(
                wallet_addresses=Config.WALLET_ADDRESSES[:1],  # 只測試第一個地址
                websocket_url=Config.WEBSOCKET_URL
            )
            
            # 嘗試連接
            await client.connect()
            
            if client.is_connected:
                print("✅ WebSocket 連接測試通過")
                await client.disconnect()
                return True
            else:
                print("❌ WebSocket 連接測試失敗")
                return False
                
        except Exception as e:
            print(f"❌ WebSocket 連接測試失敗: {e}")
            return False
            
    async def send_test_trade_alert(self):
        """發送測試交易提醒"""
        print("🧪 發送測試交易提醒...")
        
        try:
            bot = TelegramBot(
                api_id=Config.TELEGRAM_API_ID,
                api_hash=Config.TELEGRAM_API_HASH,
                bot_token=Config.TELEGRAM_BOT_TOKEN,
                chat_id=Config.TELEGRAM_CHAT_ID
            )
            
            await bot.start()
            
            # 模擬交易數據
            test_trades = [
                {
                    'wallet': '******************************************',
                    'symbol': 'BTC',
                    'action': 'OPEN',
                    'side': 'LONG',
                    'size': '1.5',
                    'price': '45000.50',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'raw_data': {'dir': 'Open Long'}
                },
                {
                    'wallet': '******************************************',
                    'symbol': 'ETH',
                    'action': 'CLOSE',
                    'side': 'SHORT',
                    'size': '10.0',
                    'price': '3200.75',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'raw_data': {'dir': 'Close Short'}
                }
            ]
            
            success_count = 0
            for i, trade in enumerate(test_trades):
                success = await bot.send_trade_alert(trade)
                if success:
                    success_count += 1
                    print(f"  - 測試交易 {i+1}: ✅")
                else:
                    print(f"  - 測試交易 {i+1}: ❌")
                    
                await asyncio.sleep(1)  # 避免頻率限制
                
            await bot.stop()
            
            if success_count == len(test_trades):
                print("✅ 測試交易提醒發送成功")
                return True
            else:
                print(f"⚠️ 部分測試交易提醒發送失敗 ({success_count}/{len(test_trades)})")
                return False
                
        except Exception as e:
            print(f"❌ 測試交易提醒發送失敗: {e}")
            return False
            
    async def run_all_tests(self):
        """運行所有測試"""
        print("🚀 開始系統測試...\n")
        
        tests = [
            ("配置驗證", self.test_config_validation),
            ("訊息格式化", self.test_message_formatter),
            ("Telegram Bot", self.test_telegram_bot),
            ("WebSocket 連接", self.test_websocket_connection),
            ("測試交易提醒", self.send_test_trade_alert)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            result = await test_func()
            results.append((test_name, result))
            
        # 顯示測試結果摘要
        print(f"\n{'='*50}")
        print("📊 測試結果摘要:")
        print(f"{'='*50}")
        
        passed = 0
        for test_name, result in results:
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"{test_name:<20} {status}")
            if result:
                passed += 1
                
        print(f"\n總計: {passed}/{len(results)} 項測試通過")
        
        if passed == len(results):
            print("🎉 所有測試通過！系統準備就緒。")
        else:
            print("⚠️ 部分測試失敗，請檢查配置和網路連接。")
            
        return passed == len(results)

async def main():
    """主測試函數"""
    try:
        tester = SystemTester()
        success = await tester.run_all_tests()
        
        if success:
            print("\n✅ 系統測試完成，可以啟動主程序：")
            print("python main.py")
        else:
            print("\n❌ 系統測試失敗，請修復問題後重新測試。")
            
    except KeyboardInterrupt:
        print("\n⚠️ 測試被用戶中斷")
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    asyncio.run(main())
