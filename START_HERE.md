# 🚀 開始使用 - 快速指南

## 📋 你需要準備的信息

### 必須項目：
1. **Telegram Bot Token** - 從 @BotFather 獲取
2. **Telegram Chat ID** - 你要發送訊息的群組 ID (負數)

**就這麼簡單！不需要 API ID/Hash 或 String Session**

## 🔧 快速設置步驟

### 步驟 1: 安裝依賴
```bash
pip install -r requirements.txt
```

### 步驟 2: 快速測試
```bash
python simple_test.py
```
這會測試 Telegram 連接和持倉查詢功能。

### 步驟 3: 啟動系統
```bash
python main.py
```

## 📱 獲取 Telegram 信息

### 1. 創建 Bot Token
1. 在 Telegram 找到 @BotFather
2. 發送 `/newbot`
3. 按提示設置名稱
4. 獲取 Token (格式: `123456789:ABCdefGHI...`)

### 2. 獲取 Chat ID
1. 將 Bot 加入你的群組
2. 在群組發送任意訊息
3. 訪問: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. 找到 `"chat":{"id":-1001234567890}` 中的 ID

### 3. 完成！
不需要額外的 API 配置，只要有 Bot Token 和 Chat ID 就可以了！

## 🎯 系統功能

✅ **實時監聽**: 監聽 9 個精選錢包的開關倉活動
✅ **智能過濾**: 自動過濾加倉訊號，只顯示新的開倉和平倉
✅ **每日持倉**: 每天12點查詢所有錢包的當前持倉
✅ **隱私保護**: 使用 SM1-SM9 代替真實錢包地址
✅ **格式化訊息**: 美觀的 Telegram 訊息格式
✅ **自動重連**: 網路斷線自動重連

## 📊 監聽的錢包地址

系統預設監聽以下 9 個錢包：
```
0x6d3b90747dbf5883bf88ff7eb5fcc86f408b5409
0xab6a57935927f4f186f098df3343fd255b7f8043
0xc5e4c250d782d8d99205b9edf13f1ab61e364ae1
0x7290ab23b5a49b5200494b89aafa28235142b805
0xf0d3384717f4d34552c8fb9c2ac283c993a033f0
0x654d8c01f308d670d6bed13d892ee7ee285028a6
0x8c2c2885a411560ff5d6cef02c276ed777f8a3de
0xd68cc8fdccc449e630e8cd2380fb6f704b99f8ad
0xd7a11a9e063b38108a0a5a0c0dae6d293bccf957
```

## 🔧 配置文件範例

創建 `.env` 文件：
```env
# 簡化配置 (只需要這兩個)
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=-1001234567890

# 其他配置 (可選)
LOG_LEVEL=INFO
```

## 🧪 測試功能

運行 `python simple_test.py` 可以測試：
- ✅ Telegram 連接
- ✅ 持倉查詢
- ✅ 錢包別名功能
- ✅ 發送訊息到群組

## 📱 訊息格式範例

### 交易提醒
```
🟢 開倉

👤 錢包: SM1
🪙 幣種: BTC
📈 做多
📊 數量: 1.5000
💵 價格: $45000.5000
💰 價值: $67,500.75
⏰ 時間: 2024-01-15 14:30:25

🔗 查看詳情
```

### 持倉查詢 (每日12點)
```
📊 當前持倉查詢

👤 錢包: SM1
  📈 BTC LONG
    💎 數量: 1.5000
    💵 入場價: $45000.5000
    🟢 未實現盈虧: $1,500.00

📈 總計: 3 個持倉
⏰ 查詢時間: 2024-01-15 12:00:00
```

## ❓ 常見問題

**Q: Bot 無法發送訊息？**  
A: 確認 Bot 在群組中且有發送訊息權限

**Q: 沒有收到交易通知？**  
A: 檢查錢包地址和網路連接，運行測試確認

**Q: 為什麼使用 SM1-SM9 而不是真實地址？**
A: 保護錢包地址隱私，避免被公開追蹤

## 🚀 開始使用

1. 運行 `python simple_test.py` 進行初始設置和測試
2. 確認所有測試通過後，運行 `python main.py` 啟動系統
3. 系統會發送啟動訊息到你的群組
4. 開始監聽錢包活動！

---

**🎉 準備好開始跟單了嗎？運行測試腳本開始吧！**
