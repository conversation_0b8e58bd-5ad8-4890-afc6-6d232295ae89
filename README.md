# Hyperliquid 錢包監聽系統

這是一個用於監聽 Hyperliquid 平台上指定錢包交易活動並自動轉發到 Telegram 群組的系統。

## 功能特色

- 🔍 **實時監聽**: 監聽多個精選錢包的開關倉活動
- 🚫 **智能過濾**: 自動過濾加倉訊號，只顯示開倉和平倉
- 📱 **Telegram 整合**: 自動格式化並發送到指定 Telegram 群組
- 🔄 **穩定重連**: 自動重連機制確保服務穩定運行
- 📊 **詳細日誌**: 完整的操作日誌記錄

## 快速開始

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

### 2. 配置環境變數

複製 `.env.example` 為 `.env` 並填入你的配置：

```bash
cp .env.example .env
```

### 3. 獲取必要的 API 金鑰

#### Telegram Bot 設置
1. 找 @BotFather 創建新的 Bot
2. 獲取 Bot Token
3. 將 Bot 加入你的群組並設為管理員
4. 獲取群組 Chat ID

#### Telegram API 設置
1. 前往 https://my.telegram.org/apps
2. 創建新應用獲取 API ID 和 API Hash

### 4. 配置錢包地址

在 `.env` 文件中設置要監聽的錢包地址：

```
WALLET_ADDRESSES=0x123...,0x456...,0x789...
```

### 5. 運行系統

```bash
python main.py
```

## 項目結構

```
hyperliquid_tracker/
├── main.py              # 主程序入口
├── config.py            # 配置管理
├── hyperliquid_client.py # Hyperliquid WebSocket 客戶端
├── telegram_bot.py      # Telegram Bot 功能
├── message_formatter.py # 訊息格式化
├── requirements.txt     # 依賴列表
├── .env.example        # 環境變數模板
└── README.md           # 說明文件
```

## 詳細設置指南

### 獲取 Telegram API 憑證

#### 1. 創建 Telegram Bot
1. 在 Telegram 中找到 @BotFather
2. 發送 `/newbot` 命令
3. 按提示設置 Bot 名稱和用戶名
4. 獲取 Bot Token (格式: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

#### 2. 獲取 Telegram API ID 和 Hash
1. 前往 https://my.telegram.org/apps
2. 登錄你的 Telegram 帳號
3. 創建新應用
4. 獲取 `api_id` (數字) 和 `api_hash` (字符串)

#### 3. 獲取群組 Chat ID
1. 將你的 Bot 加入目標群組
2. 設置 Bot 為管理員（至少需要發送訊息權限）
3. 在群組中發送一條訊息
4. 訪問 `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
5. 在返回的 JSON 中找到 `chat.id`（負數，如 `-1001234567890`）

### 配置錢包地址

在 `.env` 文件中設置要監聽的錢包地址：

```env
WALLET_ADDRESSES=******************************************,******************************************,******************************************
```

**注意**:
- 地址之間用逗號分隔，不要有空格
- 確保地址格式正確（0x 開頭的 42 字符十六進制）
- 最多建議監聽 20 個地址以避免性能問題

## 部署選項

### 選項 1: 本地運行（推薦用於測試）

```bash
# 克隆或下載項目
cd hyperliquid_tracker

# 安裝依賴
pip install -r requirements.txt

# 配置環境變數
cp .env.example .env
# 編輯 .env 文件填入你的配置

# 運行
python main.py
```

### 選項 2: Docker 部署

創建 `Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

運行命令:
```bash
docker build -t hyperliquid-tracker .
docker run -d --env-file .env hyperliquid-tracker
```

### 選項 3: 雲端部署

#### Railway 部署
1. 前往 https://railway.app
2. 連接 GitHub 倉庫
3. 在環境變數中設置所有 `.env` 中的值
4. 部署

#### AWS EC2 部署
1. 創建 EC2 實例（推薦 t3.micro）
2. 安裝 Python 3.11+
3. 上傳代碼並安裝依賴
4. 使用 systemd 或 supervisor 管理進程

#### Heroku 部署
1. 創建 `Procfile`: `worker: python main.py`
2. 設置環境變數
3. 部署到 Heroku

### 選項 4: VPS 部署（推薦用於生產）

```bash
# 在 VPS 上安裝依賴
sudo apt update
sudo apt install python3.11 python3-pip git

# 克隆項目
git clone <your-repo-url>
cd hyperliquid_tracker

# 安裝 Python 依賴
pip3 install -r requirements.txt

# 配置環境變數
cp .env.example .env
nano .env  # 編輯配置

# 使用 screen 或 tmux 在後台運行
screen -S hyperliquid
python3 main.py
# Ctrl+A+D 分離會話

# 或者使用 systemd 服務
sudo nano /etc/systemd/system/hyperliquid-tracker.service
```

systemd 服務文件範例:
```ini
[Unit]
Description=Hyperliquid Tracker
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/hyperliquid_tracker
Environment=PATH=/usr/bin:/usr/local/bin
ExecStart=/usr/bin/python3 main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## 監控和維護

### 日誌監控
```bash
# 實時查看日誌
tail -f hyperliquid_tracker.log

# 查看錯誤日誌
grep ERROR hyperliquid_tracker.log

# 日誌輪轉（自動處理，保留 7 天）
```

### 性能監控
- 系統會自動發送健康狀態到 Telegram
- 每小時發送統計信息
- 監控內存和 CPU 使用情況

### 故障排除

#### 常見問題

1. **WebSocket 連接失敗**
   ```
   解決方案:
   - 檢查網路連接
   - 確認 Hyperliquid API 狀態
   - 檢查防火牆設置
   ```

2. **Telegram 訊息發送失敗**
   ```
   解決方案:
   - 確認 Bot Token 正確
   - 檢查 Bot 是否在群組中
   - 確認 Bot 有發送訊息權限
   - 檢查 Chat ID 是否正確
   ```

3. **錢包地址無交易數據**
   ```
   解決方案:
   - 確認地址格式正確
   - 檢查地址是否在 Hyperliquid 上活躍
   - 確認 WebSocket 訂閱成功
   ```

4. **內存使用過高**
   ```
   解決方案:
   - 減少監聽的錢包數量
   - 重啟服務
   - 檢查是否有內存洩漏
   ```

### 更新和維護
```bash
# 停止服務
pkill -f main.py

# 更新代碼
git pull

# 重新安裝依賴（如有更新）
pip install -r requirements.txt

# 重啟服務
python main.py
```

## 安全建議

1. **API 金鑰安全**
   - 不要將 `.env` 文件提交到版本控制
   - 定期輪換 API 金鑰
   - 使用環境變數或密鑰管理服務

2. **服務器安全**
   - 使用防火牆限制訪問
   - 定期更新系統和依賴
   - 使用非 root 用戶運行服務

3. **監控安全**
   - 監控異常登錄和 API 調用
   - 設置告警機制
   - 定期檢查日誌

## 支援和社群

- 📧 技術支援: [聯繫信息]
- 📚 文檔: 查看 README 和代碼註釋
- 🐛 問題報告: 通過 GitHub Issues
- 💬 社群討論: [Telegram 群組或 Discord]
