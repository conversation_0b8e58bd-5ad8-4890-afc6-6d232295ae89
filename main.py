"""
Hyperliquid 錢包監聽系統主程序
監聽指定錢包的開關倉活動並轉發到 Telegram 群組
"""
import asyncio
import signal
import sys
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, List
from loguru import logger

from config import Config
from hyperliquid_client import HyperliquidClient
from telegram_bot import TelegramBot
from message_formatter import MessageFormatter
from error_handler import ConnectionManager, HealthChecker, async_error_handler
from position_tracker import PositionTracker

class HyperliquidTracker:
    def __init__(self):
        # 驗證配置
        Config.validate()
        
        # 初始化組件
        self.hyperliquid_client = HyperliquidClient(
            wallet_addresses=Config.WALLET_ADDRESSES,
            websocket_url=Config.WEBSOCKET_URL
        )
        
        self.telegram_bot = TelegramBot(
            bot_token=Config.TELEGRAM_BOT_TOKEN,
            chat_id=Config.TELEGRAM_CHAT_ID
        )
        
        self.message_formatter = MessageFormatter()

        # 持倉追蹤器
        self.position_tracker = PositionTracker(Config.WALLET_ADDRESSES)

        # 連接管理器
        self.hyperliquid_manager = ConnectionManager("Hyperliquid WebSocket")
        self.telegram_manager = ConnectionManager("Telegram Bot")

        # 健康檢查器
        self.health_checker = HealthChecker(check_interval=60.0)
        
        # 統計數據
        self.stats = {
            'total_trades': 0,
            'open_trades': 0,
            'close_trades': 0,
            'filtered_trades': 0,
            'start_time': datetime.now(),
            'last_trade_time': None
        }
        
        # 運行狀態
        self.is_running = False

    def _handle_task_exception(self, task):
        """處理異步任務異常"""
        try:
            if task.exception():
                logger.error(f"異步任務發生異常: {task.exception()}")
                logger.error(f"異常詳情: {traceback.format_exception(type(task.exception()), task.exception(), task.exception().__traceback__)}")
        except Exception as e:
            logger.error(f"處理任務異常時發生錯誤: {e}")

    async def setup_logging(self):
        """設置日誌"""
        logger.remove()  # 移除默認處理器
        
        # 控制台輸出
        logger.add(
            sys.stdout,
            level=Config.LOG_LEVEL,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        
        # 文件輸出
        logger.add(
            Config.LOG_FILE,
            level=Config.LOG_LEVEL,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days"
        )
        
        logger.info("日誌系統初始化完成")
        
    async def setup_health_checks(self):
        """設置健康檢查"""
        async def check_hyperliquid():
            try:
                # 檢查連接狀態和基本功能
                if not self.hyperliquid_client.is_connected:
                    return False
                # 可以添加更多檢查，比如簡單的 API 調用
                return True
            except Exception:
                return False

        async def check_telegram():
            try:
                # 檢查 Telegram bot 是否就緒
                return self.telegram_bot.is_ready()
            except Exception:
                return False

        self.health_checker.register_component("hyperliquid", check_hyperliquid)
        self.health_checker.register_component("telegram", check_telegram)
        
    async def handle_trade_event(self, trade_info: Dict[str, Any]):
        """處理交易事件"""
        try:
            logger.debug(f"收到交易事件: {trade_info}")
            
            # 驗證交易信息
            if not self.message_formatter.validate_trade_info(trade_info):
                logger.warning("交易信息驗證失敗，跳過處理")
                return
                
            # 檢查是否應該過濾
            if self.message_formatter.should_filter_message(trade_info):
                self.stats['filtered_trades'] += 1
                logger.debug("交易被過濾，不發送通知")
                return
                
            # 更新統計
            self.stats['total_trades'] += 1
            self.stats['last_trade_time'] = datetime.now()
            
            if trade_info['action'] == 'OPEN':
                self.stats['open_trades'] += 1
            elif trade_info['action'] == 'CLOSE':
                self.stats['close_trades'] += 1
                
            # 發送到 Telegram
            success = await self.telegram_bot.send_trade_alert(trade_info)
            
            if success:
                logger.info(f"交易通知發送成功: {trade_info['symbol']} {trade_info['action']}")
            else:
                logger.error("交易通知發送失敗")
                
        except Exception as e:
            logger.error(f"處理交易事件時發生錯誤: {e}")
            
    async def start_hyperliquid_client(self):
        """啟動 Hyperliquid 客戶端"""
        try:
            # 添加交易事件回調
            self.hyperliquid_client.add_callback(self.handle_trade_event)

            # 嘗試連接
            connection_success = await self.hyperliquid_manager.connect(self.hyperliquid_client.connect)

            if connection_success:
                # 開始監聽（帶重連）
                hyperliquid_task = asyncio.create_task(self.hyperliquid_client.run_with_reconnect())
                hyperliquid_task.add_done_callback(self._handle_task_exception)
                logger.info("Hyperliquid 客戶端啟動成功，開始監聽")
            else:
                logger.warning("Hyperliquid 客戶端初始連接失敗，將在後台嘗試重連")
                # 即使初始連接失敗，也啟動重連任務
                hyperliquid_task = asyncio.create_task(self.hyperliquid_client.run_with_reconnect())
                hyperliquid_task.add_done_callback(self._handle_task_exception)

        except Exception as e:
            logger.error(f"啟動 Hyperliquid 客戶端時發生錯誤: {e}")
            # 不拋出異常，讓系統繼續運行
            # 啟動重連任務作為備用
            hyperliquid_task = asyncio.create_task(self.hyperliquid_client.run_with_reconnect())
            hyperliquid_task.add_done_callback(self._handle_task_exception)
        
    async def start_telegram_bot(self):
        """啟動 Telegram Bot"""
        connection_success = await self.telegram_manager.connect(self.telegram_bot.start)
        if not connection_success:
            raise Exception("Telegram Bot 連接失敗")
        
    async def health_alert_callback(self, unhealthy_components: List[str], health_results: Dict):
        """健康檢查告警回調"""
        # 只在真正有錯誤時才發送告警，忽略初始化期間的暫時性問題
        critical_issues = []
        for component in unhealthy_components:
            status = health_results[component]['status']
            # 只有在明確錯誤時才告警，不包括初始化時的 unhealthy 狀態
            if status == 'error':
                critical_issues.append(component)

        # 只有在有真正的錯誤且 Telegram 已就緒時才發送告警
        if critical_issues and self.telegram_bot.is_ready():
            message = f"🚨 **系統錯誤告警**\n\n錯誤組件: {', '.join(critical_issues)}"
            await self.telegram_bot.send_system_message(message, "ERROR")
            
    async def send_daily_stats(self):
        """發送每日統計"""
        try:
            uptime = datetime.now() - self.stats['start_time']
            
            stats_message = {
                'total_trades': self.stats['total_trades'],
                'open_trades': self.stats['open_trades'], 
                'close_trades': self.stats['close_trades'],
                'filtered_trades': self.stats['filtered_trades'],
                'uptime_hours': uptime.total_seconds() / 3600,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            await self.telegram_bot.send_statistics(stats_message)
            
        except Exception as e:
            logger.error(f"發送統計信息失敗: {e}")
            
    async def start(self):
        """啟動系統"""
        logger.info("🚀 啟動 Hyperliquid 錢包監聽系統...")

        startup_errors = []

        # 設置日誌
        try:
            await self.setup_logging()
            logger.info("✅ 日誌系統初始化完成")
        except Exception as e:
            error_msg = f"日誌系統初始化失敗: {e}"
            logger.error(error_msg)
            startup_errors.append(error_msg)

        # 設置健康檢查
        try:
            await self.setup_health_checks()
            logger.info("✅ 健康檢查系統初始化完成")
        except Exception as e:
            error_msg = f"健康檢查系統初始化失敗: {e}"
            logger.error(error_msg)
            startup_errors.append(error_msg)

        # 啟動 Telegram Bot
        telegram_started = False
        try:
            await self.start_telegram_bot()
            telegram_started = True
            logger.info("✅ Telegram Bot 啟動成功")
        except Exception as e:
            error_msg = f"Telegram Bot 啟動失敗: {e}"
            logger.error(error_msg)
            startup_errors.append(error_msg)

        # 啟動 Hyperliquid 客戶端
        hyperliquid_started = False
        try:
            await self.start_hyperliquid_client()
            hyperliquid_started = True
            logger.info("✅ Hyperliquid 客戶端啟動成功")
        except Exception as e:
            error_msg = f"Hyperliquid 客戶端啟動失敗: {e}"
            logger.error(error_msg)
            startup_errors.append(error_msg)

        # 檢查關鍵組件是否啟動成功
        if not telegram_started:
            logger.error("❌ Telegram Bot 啟動失敗，系統將以降級模式運行")
            startup_errors.append("Telegram Bot 啟動失敗 - 無法發送通知")

        if not hyperliquid_started:
            logger.warning("⚠️ Hyperliquid 客戶端啟動失敗，將嘗試重連")

        # 啟動健康監控
        try:
            health_task = asyncio.create_task(
                self.health_checker.start_monitoring(self.health_alert_callback)
            )
            # 添加異常處理回調
            health_task.add_done_callback(self._handle_task_exception)
            logger.info("✅ 健康監控已啟動")
        except Exception as e:
            error_msg = f"健康監控啟動失敗: {e}"
            logger.error(error_msg)
            startup_errors.append(error_msg)

        # 定期發送統計（每小時）
        try:
            stats_task = asyncio.create_task(self.periodic_stats())
            stats_task.add_done_callback(self._handle_task_exception)
            logger.info("✅ 統計任務已啟動")
        except Exception as e:
            error_msg = f"統計任務啟動失敗: {e}"
            logger.error(error_msg)
            startup_errors.append(error_msg)

        # 每日持倉查詢（每天12點）
        try:
            position_task = asyncio.create_task(self.daily_position_check())
            position_task.add_done_callback(self._handle_task_exception)
            logger.info("✅ 每日持倉查詢任務已啟動")
        except Exception as e:
            error_msg = f"每日持倉查詢任務啟動失敗: {e}"
            logger.error(error_msg)
            startup_errors.append(error_msg)

        self.is_running = True

        # 準備啟動通知
        wallet_count = len(Config.WALLET_ADDRESSES)
        status_summary = "✅ 完全正常" if not startup_errors else f"⚠️ 部分組件異常 ({len(startup_errors)} 個錯誤)"

        start_message = f"""🚀 系統啟動成功

📊 監聽錢包數量: {wallet_count}
⏰ 啟動時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 配置: 過濾加倉訊號，只顯示開關倉
📈 系統狀態: {status_summary}

系統已開始監聽精選錢包的交易活動！"""

        if startup_errors:
            start_message += f"\n\n⚠️ **啟動警告:**\n" + "\n".join([f"• {error}" for error in startup_errors])

        # 發送啟動通知
        if telegram_started:
            try:
                await self.telegram_bot.send_system_message(start_message, "SUCCESS" if not startup_errors else "WARNING")
                logger.info("✅ 啟動通知已發送")

                # 發送測試交易通知（用於驗證系統正常工作）
                await asyncio.sleep(2)  # 等待2秒
                test_trade = {
                    'wallet': 'TEST',
                    'symbol': 'BTC',
                    'side': 'LONG',
                    'size': '0.1',
                    'price': '50000',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'action': 'OPEN'
                }
                await self.telegram_bot.send_trade_alert(test_trade)
                logger.info("✅ 測試交易通知已發送")

            except Exception as e:
                logger.error(f"發送啟動通知失敗: {e}")
        else:
            logger.warning("⚠️ Telegram Bot 不可用，跳過啟動通知")

        # 最終狀態報告
        logger.success("✅ 系統啟動完成！開始監聽錢包活動...")
        if startup_errors:
            logger.warning(f"系統啟動完成，但有 {len(startup_errors)} 個組件存在問題")
            for error in startup_errors:
                logger.warning(f"  • {error}")
        else:
            logger.success("✅ 所有組件正常啟動！")
            
    async def periodic_stats(self):
        """定期發送統計信息"""
        while self.is_running:
            try:
                await asyncio.sleep(3600)  # 每小時

                # 只有在有交易活動時才發送統計
                if self.stats['total_trades'] > 0:
                    await self.send_daily_stats()
                    logger.info("已發送每小時統計")
                else:
                    logger.debug("無交易活動，跳過統計發送")

            except Exception as e:
                logger.error(f"發送定期統計失敗: {e}")

    async def daily_position_check(self):
        """每日持倉查詢（每天12點）"""
        while self.is_running:
            try:
                now = datetime.now()

                # 計算到下一個12點的時間
                if now.hour < 12:
                    next_check = now.replace(hour=12, minute=0, second=0, microsecond=0)
                else:
                    next_check = (now + timedelta(days=1)).replace(hour=12, minute=0, second=0, microsecond=0)

                sleep_seconds = (next_check - now).total_seconds()
                logger.info(f"下次持倉查詢時間: {next_check.strftime('%Y-%m-%d %H:%M:%S')}")

                await asyncio.sleep(sleep_seconds)

                # 執行持倉查詢
                await self.check_and_send_positions()

            except Exception as e:
                logger.error(f"每日持倉查詢失敗: {e}")
                await asyncio.sleep(3600)  # 出錯後等待1小時再試

    async def check_and_send_positions(self):
        """查詢並發送持倉信息"""
        try:
            logger.info("開始查詢所有錢包持倉...")

            async with self.position_tracker as tracker:
                all_positions = await tracker.get_all_positions()

                if all_positions:
                    message = tracker.format_positions_message(all_positions)
                    await self.telegram_bot.send_message(message)

                    # 保存持倉快照
                    tracker.save_positions_snapshot(all_positions)

                    logger.info("持倉查詢完成並已發送到 Telegram")
                else:
                    await self.telegram_bot.send_system_message("📊 當前所有監聽錢包均無持倉", "INFO")
                    logger.info("所有監聽錢包均無持倉")

        except Exception as e:
            logger.error(f"查詢持倉時發生錯誤: {e}")
            await self.telegram_bot.send_system_message(f"❌ 持倉查詢失敗: {str(e)}", "ERROR")
                
    async def stop(self):
        """停止系統"""
        logger.info("🛑 正在停止系統...")

        self.is_running = False

        # 停止健康監控
        try:
            self.health_checker.stop_monitoring()
        except Exception as e:
            logger.error(f"停止健康監控時發生錯誤: {e}")

        # 斷開連接
        try:
            await self.hyperliquid_manager.disconnect(self.hyperliquid_client.disconnect)
        except Exception as e:
            logger.error(f"斷開 Hyperliquid 連接時發生錯誤: {e}")

        try:
            await self.telegram_manager.disconnect(self.telegram_bot.stop)
        except Exception as e:
            logger.error(f"斷開 Telegram 連接時發生錯誤: {e}")

        logger.info("✅ 系統已停止")
        
    def setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信號 {signum}，正在優雅關閉...")
            # 設置停止標誌，讓主循環自然退出
            self.is_running = False

        # 只處理 SIGINT (Ctrl+C)，不處理 SIGTERM
        signal.signal(signal.SIGINT, signal_handler)
        # 在容器環境中，SIGTERM 可能會在啟動時意外觸發，所以我們忽略它
        logger.info("信號處理器已設置 (僅處理 SIGINT)")

async def main():
    """主函數"""
    tracker = HyperliquidTracker()
    tracker.setup_signal_handlers()

    try:
        await tracker.start()
        logger.info("系統啟動完成，進入主循環")
        logger.info(f"當前 is_running 狀態: {tracker.is_running}")

        # 保持運行
        loop_count = 0
        while tracker.is_running:
            await asyncio.sleep(1)
            loop_count += 1

            # 每 60 秒記錄一次狀態
            if loop_count % 60 == 0:
                logger.debug(f"主循環運行中，已運行 {loop_count} 秒")

    except KeyboardInterrupt:
        logger.info("收到中斷信號")
    except Exception as e:
        logger.error(f"系統運行時發生錯誤: {e}")
        logger.error(f"錯誤詳情: {traceback.format_exc()}")
        print("[DEBUG] 系統運行時發生錯誤:", e)
        print("[DEBUG] 錯誤詳情:\n", traceback.format_exc())
        # 如果是啟動階段的錯誤，不要立即停止，而是嘗試恢復
        if tracker.is_running:
            logger.info("系統仍在運行狀態，嘗試繼續...")
            try:
                while tracker.is_running:
                    await asyncio.sleep(1)
            except Exception as recovery_error:
                logger.error(f"恢復過程中發生錯誤: {recovery_error}")
                print("[DEBUG] 恢復過程中發生錯誤:", recovery_error)
                print("[DEBUG] 錯誤詳情:\n", traceback.format_exc())
    finally:
        logger.info(f"主循環退出,is_running 狀態: {tracker.is_running}")
        print(f"[DEBUG] 主循環退出,is_running 狀態: {tracker.is_running}")
        if tracker.is_running:
            logger.info("正在優雅關閉系統...")
            print("[DEBUG] 正在優雅關閉系統...")
            await tracker.stop()
        else:
            logger.info("系統已經停止")
            print("[DEBUG] 系統已經停止")

if __name__ == "__main__":
    asyncio.run(main())
