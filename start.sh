#!/bin/bash

echo "🚀 啟動 Hyperliquid 錢包監聽系統..."
echo

# 檢查 Python 是否安裝
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安裝"
    echo "請安裝 Python 3.8+ 後重新運行"
    exit 1
fi

# 顯示 Python 版本
echo "✅ Python 版本: $(python3 --version)"

# 檢查是否存在 .env 文件
if [ ! -f .env ]; then
    echo "⚠️ 未找到 .env 配置文件"
    echo "請先運行 setup.py 進行配置"
    echo
    read -p "是否現在運行設置向導？(y/N): " choice
    if [[ $choice =~ ^[Yy]$ ]]; then
        python3 setup.py
    else
        echo "請手動創建 .env 文件後重新運行"
        exit 1
    fi
fi

# 檢查依賴是否安裝
echo "📦 檢查依賴..."
if ! python3 -c "import websockets, telethon, loguru" &> /dev/null; then
    echo "⚠️ 依賴未安裝，正在安裝..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 依賴安裝失敗"
        exit 1
    fi
fi

# 運行測試（可選）
read -p "是否運行系統測試？(y/N): " test_choice
if [[ $test_choice =~ ^[Yy]$ ]]; then
    echo "🧪 運行系統測試..."
    python3 test_system.py
    if [ $? -ne 0 ]; then
        echo "⚠️ 測試失敗，但仍可嘗試啟動主程序"
        read -p "按 Enter 繼續..."
    fi
fi

# 啟動主程序
echo
echo "✅ 啟動主程序..."
python3 main.py

# 如果程序異常退出
if [ $? -ne 0 ]; then
    echo
    echo "❌ 程序異常退出"
    echo "請檢查日誌文件: hyperliquid_tracker.log"
fi
