2025-07-09 16:46:45 | INFO     | __main__:setup_logging:80 - 日誌系統初始化完成
2025-07-09 16:46:45 | INFO     | error_handler:connect:68 - 正在連接 Telegram Bot...
2025-07-09 16:46:45 | INFO     | telegram_bot:start:22 - 正在啟動 Telegram Bot...
2025-07-09 16:46:46 | SUCCESS  | telegram_bot:start:35 - Telegram Bot 啟動成功: @SMtracker6_bot
2025-07-09 16:46:47 | SUCCESS  | error_handler:connect:74 - Telegram <PERSON>t 連接成功
2025-07-09 16:46:47 | INFO     | error_handler:connect:68 - 正在連接 Hyperliquid WebSocket...
2025-07-09 16:46:47 | INFO     | hyperliquid_client:connect:31 - 正在連接到 Hyperliquid WebSocket: wss://api.hyperliquid.xyz/ws
2025-07-09 16:46:47 | SUCCESS  | hyperliquid_client:connect:40 - WebSocket 連接成功
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x6d3b90747dbf5883bf88ff7eb5fcc86f408b5409 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xab6a57935927f4f186f098df3343fd255b7f8043 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xc5e4c250d782d8d99205b9edf13f1ab61e364ae1 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x7290ab23b5a49b5200494b89aafa28235142b805 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xf0d3384717f4d34552c8fb9c2ac283c993a033f0 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x654d8c01f308d670d6bed13d892ee7ee285028a6 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x8c2c2885a411560ff5d6cef02c276ed777f8a3de 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xd68cc8fdccc449e630e8cd2380fb6f704b99f8ad 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xd7a11a9e063b38108a0a5a0c0dae6d293bccf957 的填單事件
2025-07-09 16:46:47 | INFO     | hyperliquid_client:subscribe_user_fills:74 - 已訂閱所有市場數據
2025-07-09 16:46:47 | SUCCESS  | error_handler:connect:74 - Hyperliquid WebSocket 連接成功
2025-07-09 16:46:47 | SUCCESS  | __main__:start:199 - ✅ 系統啟動完成！開始監聽錢包活動...
2025-07-09 16:46:48 | INFO     | error_handler:start_monitoring:159 - 開始健康監控，檢查間隔: 60.0 秒
2025-07-09 16:46:48 | ERROR    | error_handler:check_health:152 - 健康檢查失敗 hyperliquid: object bool can't be used in 'await' expression
2025-07-09 16:46:48 | ERROR    | error_handler:check_health:152 - 健康檢查失敗 telegram: object bool can't be used in 'await' expression
2025-07-09 16:46:50 | ERROR    | __main__:daily_position_check:250 - 每日持倉查詢失敗: name 'timedelta' is not defined
2025-07-10 03:09:43 | INFO     | __main__:setup_logging:90 - 日誌系統初始化完成
2025-07-10 03:09:43 | INFO     | __main__:start:226 - ✅ 日誌系統初始化完成
2025-07-10 03:09:43 | INFO     | __main__:start:235 - ✅ 健康檢查系統初始化完成
2025-07-10 03:09:43 | INFO     | error_handler:connect:68 - 正在連接 Telegram Bot...
2025-07-10 03:09:43 | INFO     | telegram_bot:start:22 - 正在啟動 Telegram Bot...
2025-07-10 03:09:44 | SUCCESS  | telegram_bot:start:35 - Telegram Bot 啟動成功: @SMtracker6_bot
2025-07-10 03:09:45 | SUCCESS  | error_handler:connect:74 - Telegram Bot 連接成功
2025-07-10 03:09:45 | INFO     | __main__:start:246 - ✅ Telegram Bot 啟動成功
2025-07-10 03:09:45 | INFO     | error_handler:connect:68 - 正在連接 Hyperliquid WebSocket...
2025-07-10 03:09:45 | INFO     | hyperliquid_client:connect:31 - 正在連接到 Hyperliquid WebSocket: wss://api.hyperliquid.xyz/ws
2025-07-10 03:09:46 | SUCCESS  | hyperliquid_client:connect:40 - WebSocket 連接成功
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x6d3b90747dbf5883bf88ff7eb5fcc86f408b5409 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xab6a57935927f4f186f098df3343fd255b7f8043 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xc5e4c250d782d8d99205b9edf13f1ab61e364ae1 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x7290ab23b5a49b5200494b89aafa28235142b805 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xf0d3384717f4d34552c8fb9c2ac283c993a033f0 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x654d8c01f308d670d6bed13d892ee7ee285028a6 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0x8c2c2885a411560ff5d6cef02c276ed777f8a3de 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xd68cc8fdccc449e630e8cd2380fb6f704b99f8ad 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:64 - 已訂閱錢包 0xd7a11a9e063b38108a0a5a0c0dae6d293bccf957 的填單事件
2025-07-10 03:09:46 | INFO     | hyperliquid_client:subscribe_user_fills:74 - 已訂閱所有市場數據
2025-07-10 03:09:46 | SUCCESS  | error_handler:connect:74 - Hyperliquid WebSocket 連接成功
2025-07-10 03:09:46 | INFO     | __main__:start_hyperliquid_client:163 - Hyperliquid 客戶端啟動成功，開始監聽
2025-07-10 03:09:46 | INFO     | __main__:start:257 - ✅ Hyperliquid 客戶端啟動成功
2025-07-10 03:09:46 | INFO     | __main__:start:278 - ✅ 健康監控已啟動
2025-07-10 03:09:46 | INFO     | __main__:start:288 - ✅ 統計任務已啟動
2025-07-10 03:09:46 | INFO     | __main__:start:298 - ✅ 每日持倉查詢任務已啟動
2025-07-10 03:09:47 | INFO     | __main__:start:328 - ✅ 啟動通知已發送
2025-07-10 03:09:47 | SUCCESS  | __main__:start:335 - ✅ 系統啟動完成！開始監聽錢包活動...
2025-07-10 03:09:47 | SUCCESS  | __main__:start:341 - ✅ 所有組件正常啟動！
2025-07-10 03:09:47 | INFO     | __main__:main:444 - 系統啟動完成，進入主循環
2025-07-10 03:09:47 | INFO     | __main__:main:445 - 當前 is_running 狀態: True
2025-07-10 03:09:47 | INFO     | error_handler:start_monitoring:159 - 開始健康監控，檢查間隔: 60.0 秒
2025-07-10 03:09:47 | INFO     | __main__:daily_position_check:365 - 下次持倉查詢時間: 2025-07-10 12:00:00
2025-07-10 12:00:00 | INFO     | __main__:check_and_send_positions:379 - 開始查詢所有錢包持倉...
2025-07-10 12:00:00 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0x6d3b9074...
2025-07-10 12:00:00 | INFO     | position_tracker:get_all_positions:121 - 錢包 0x6d3b9074... 有 17 個持倉
2025-07-10 12:00:00 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0xab6a5793...
2025-07-10 12:00:03 | INFO     | position_tracker:get_all_positions:121 - 錢包 0xab6a5793... 有 1 個持倉
2025-07-10 12:00:03 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0xc5e4c250...
2025-07-10 12:00:03 | INFO     | position_tracker:get_all_positions:121 - 錢包 0xc5e4c250... 有 4 個持倉
2025-07-10 12:00:04 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0x7290ab23...
2025-07-10 12:00:04 | INFO     | position_tracker:get_all_positions:121 - 錢包 0x7290ab23... 有 2 個持倉
2025-07-10 12:00:04 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0xf0d33847...
2025-07-10 12:00:04 | INFO     | position_tracker:get_all_positions:123 - 錢包 0xf0d33847... 無持倉
2025-07-10 12:00:05 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0x654d8c01...
2025-07-10 12:00:05 | INFO     | position_tracker:get_all_positions:121 - 錢包 0x654d8c01... 有 1 個持倉
2025-07-10 12:00:06 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0x8c2c2885...
2025-07-10 12:00:06 | INFO     | position_tracker:get_all_positions:121 - 錢包 0x8c2c2885... 有 1 個持倉
2025-07-10 12:00:07 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0xd68cc8fd...
2025-07-10 12:00:07 | INFO     | position_tracker:get_all_positions:121 - 錢包 0xd68cc8fd... 有 1 個持倉
2025-07-10 12:00:07 | INFO     | position_tracker:get_all_positions:114 - 查詢錢包持倉: 0xd7a11a9e...
2025-07-10 12:00:07 | INFO     | position_tracker:get_all_positions:121 - 錢包 0xd7a11a9e... 有 3 個持倉
2025-07-10 12:00:10 | INFO     | position_tracker:save_positions_snapshot:216 - 持倉快照已保存到: positions_snapshot_20250710_120010.json
2025-07-10 12:00:10 | INFO     | __main__:check_and_send_positions:391 - 持倉查詢完成並已發送到 Telegram
2025-07-10 12:00:10 | INFO     | __main__:daily_position_check:365 - 下次持倉查詢時間: 2025-07-11 12:00:00
2025-07-10 12:56:05 | WARNING  | hyperliquid_client:listen:94 - WebSocket 連接已關閉
2025-07-10 12:56:07 | INFO     | hyperliquid_client:connect:31 - 正在連接到 Hyperliquid WebSocket: wss://api.hyperliquid.xyz/ws
2025-07-10 12:56:07 | ERROR    | hyperliquid_client:connect:46 - WebSocket 連接失敗: [Errno 11001] getaddrinfo failed
2025-07-10 12:56:07 | ERROR    | hyperliquid_client:run_with_reconnect:201 - 運行時發生錯誤: [Errno 11001] getaddrinfo failed
2025-07-10 12:56:07 | INFO     | hyperliquid_client:reconnect:181 - 嘗試重連 (第 1 次)
2025-07-10 12:56:12 | INFO     | hyperliquid_client:connect:31 - 正在連接到 Hyperliquid WebSocket: wss://api.hyperliquid.xyz/ws
2025-07-10 12:56:12 | ERROR    | hyperliquid_client:connect:46 - WebSocket 連接失敗: [Errno 11001] getaddrinfo failed
2025-07-10 12:56:12 | ERROR    | hyperliquid_client:reconnect:188 - 重連失敗: [Errno 11001] getaddrinfo failed
2025-07-10 12:56:12 | ERROR    | hyperliquid_client:run_with_reconnect:206 - 重連失敗，程序退出
2025-07-10 15:21:26 | ERROR    | telegram_bot:send_message:91 - 發送訊息失敗: HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot7825520217:AAGmxZJnKtsBiRIIK2mL_wjr5RbSKnda9Q0/sendMessage (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000024EE171F8C8>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-07-10 15:21:28 | ERROR    | telegram_bot:send_message:92 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\connection.py", line 175, in _new_conn
    (self._dns_host, self.port), self.timeout, **extra_kw
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\util\connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\socket.py", line 752, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\connectionpool.py", line 723, in urlopen
    chunked=chunked,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\connectionpool.py", line 1061, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\connection.py", line 187, in _new_conn
    self, "Failed to establish a new connection: %s" % e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x0000024EE171F8C8>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\requests\adapters.py", line 499, in send
    timeout=timeout,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\connectionpool.py", line 803, in urlopen
    method, url, error=e, _pool=self, _stacktrace=sys.exc_info()[2]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\urllib3\util\retry.py", line 594, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot7825520217:AAGmxZJnKtsBiRIIK2mL_wjr5RbSKnda9Q0/sendMessage (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000024EE171F8C8>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\smc track\telegram_bot.py", line 67, in send_message
    response = requests.post(url, data=data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\requests\sessions.py", line 587, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\requests\sessions.py", line 701, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python37\lib\site-packages\requests\adapters.py", line 565, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot7825520217:AAGmxZJnKtsBiRIIK2mL_wjr5RbSKnda9Q0/sendMessage (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000024EE171F8C8>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

