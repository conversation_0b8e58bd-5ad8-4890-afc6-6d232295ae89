"""
Hyperliquid WebSocket 客戶端
用於監聽指定錢包的交易事件
"""
import asyncio
import json
import websockets
from typing import List, Callable, Optional, Dict, Any
from loguru import logger
from datetime import datetime
import traceback

class HyperliquidClient:
    def __init__(self, wallet_addresses: List[str], websocket_url: str = "wss://api.hyperliquid.xyz/ws"):
        self.wallet_addresses = [addr.lower() for addr in wallet_addresses]
        self.websocket_url = websocket_url
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5
        self.callbacks = []
        
    def add_callback(self, callback: Callable):
        """添加事件回調函數"""
        self.callbacks.append(callback)
        
    async def connect(self):
        """建立 WebSocket 連接"""
        try:
            logger.info(f"正在連接到 Hyperliquid WebSocket: {self.websocket_url}")
            self.websocket = await websockets.connect(
                self.websocket_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )
            self.is_connected = True
            self.reconnect_attempts = 0
            logger.success("WebSocket 連接成功")
            
            # 訂閱用戶填單事件
            await self.subscribe_user_fills()
            
        except Exception as e:
            logger.error(f"WebSocket 連接失敗: {e}")
            self.is_connected = False
            raise
            
    async def subscribe_user_fills(self):
        """訂閱用戶填單事件"""
        try:
            # 為每個錢包地址訂閱填單事件
            for address in self.wallet_addresses:
                subscribe_msg = {
                    "method": "subscribe",
                    "subscription": {
                        "type": "userFills",
                        "user": address
                    }
                }
                
                await self.websocket.send(json.dumps(subscribe_msg))
                logger.info(f"已訂閱錢包 {address} 的填單事件")
                
            # 也可以訂閱所有用戶事件然後過濾
            all_fills_msg = {
                "method": "subscribe", 
                "subscription": {
                    "type": "allMids"
                }
            }
            await self.websocket.send(json.dumps(all_fills_msg))
            logger.info("已訂閱所有市場數據")
            
        except Exception as e:
            logger.error(f"訂閱事件失敗: {e}")
            raise
            
    async def listen(self):
        """監聽 WebSocket 消息"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON 解析失敗: {e}")
                except Exception as e:
                    logger.error(f"處理消息時發生錯誤: {e}")
                    logger.error(traceback.format_exc())
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket 連接已關閉")
            self.is_connected = False
        except Exception as e:
            logger.error(f"監聽消息時發生錯誤: {e}")
            self.is_connected = False
            
    async def handle_message(self, data: Dict[Any, Any]):
        """處理接收到的消息"""
        try:
            # 檢查是否是填單事件
            if data.get("channel") == "userFills" or "fills" in data:
                fills_data = data.get("data", {}).get("fills", []) if "data" in data else data.get("fills", [])
                
                for fill in fills_data:
                    user_address = fill.get("user", "").lower()
                    
                    # 只處理我們監聽的錢包地址
                    if user_address in self.wallet_addresses:
                        await self.process_fill(fill)
                        
            # 處理其他類型的消息
            elif data.get("channel") == "allMids":
                # 處理市場數據更新
                pass
                
        except Exception as e:
            logger.error(f"處理消息失敗: {e}")
            logger.error(f"消息內容: {data}")
            
    async def process_fill(self, fill_data: Dict[Any, Any]):
        """處理填單數據"""
        try:
            # 提取關鍵信息
            trade_info = {
                'wallet': fill_data.get('user', ''),
                'symbol': fill_data.get('coin', ''),
                'side': 'LONG' if fill_data.get('dir') == 'Open Long' else 'SHORT' if fill_data.get('dir') == 'Open Short' else fill_data.get('dir', ''),
                'size': fill_data.get('sz', '0'),
                'price': fill_data.get('px', '0'),
                'timestamp': datetime.fromtimestamp(fill_data.get('time', 0) / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                'action': self.determine_action(fill_data),
                'raw_data': fill_data
            }
            
            # 只處理開倉和平倉，過濾加倉
            if trade_info['action'] in ['OPEN', 'CLOSE']:
                logger.info(f"檢測到 {trade_info['action']} 訊號: {trade_info['wallet']} {trade_info['symbol']} {trade_info['side']} {trade_info['size']}")
                
                # 調用所有回調函數
                for callback in self.callbacks:
                    try:
                        await callback(trade_info)
                    except Exception as e:
                        logger.error(f"回調函數執行失敗: {e}")
            else:
                logger.debug(f"過濾掉加倉訊號: {trade_info}")
                
        except Exception as e:
            logger.error(f"處理填單數據失敗: {e}")
            logger.error(f"填單數據: {fill_data}")
            
    def determine_action(self, fill_data: Dict[Any, Any]) -> str:
        """判斷交易動作類型"""
        direction = fill_data.get('dir', '').lower()
        
        if 'open' in direction:
            return 'OPEN'
        elif 'close' in direction:
            return 'CLOSE'
        else:
            # 根據其他字段判斷，如果無法確定則標記為加倉
            return 'ADD'
            
    async def disconnect(self):
        """斷開 WebSocket 連接"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            logger.info("WebSocket 連接已斷開")
            
    async def reconnect(self):
        """重新連接"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"重連次數已達上限 ({self.max_reconnect_attempts})，停止重連")
            return False
            
        self.reconnect_attempts += 1
        logger.info(f"嘗試重連 (第 {self.reconnect_attempts} 次)")
        
        try:
            await asyncio.sleep(self.reconnect_delay)
            await self.connect()
            return True
        except Exception as e:
            logger.error(f"重連失敗: {e}")
            return False
            
    async def run_with_reconnect(self):
        """帶重連功能的運行方法"""
        while True:
            try:
                if not self.is_connected:
                    await self.connect()
                    
                await self.listen()
                
            except Exception as e:
                logger.error(f"運行時發生錯誤: {e}")
                self.is_connected = False
                
                # 嘗試重連
                if not await self.reconnect():
                    logger.error("重連失敗，程序退出")
                    break
                    
            await asyncio.sleep(1)
