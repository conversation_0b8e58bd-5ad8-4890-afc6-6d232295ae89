"""
簡單測試腳本 - 測試 Telegram 和持倉查詢
"""
import asyncio
from config import Config
from telegram_bot import TelegramBot
from position_tracker import PositionTracker

async def test_telegram():
    """測試 Telegram 連接"""
    print("🧪 測試 Telegram 連接...")
    
    try:
        bot = TelegramBot(
            bot_token=Config.TELEGRAM_BOT_TOKEN,
            chat_id=Config.TELEGRAM_CHAT_ID
        )
        
        await bot.start()
        
        # 發送測試訊息
        test_message = """🧪 **系統測試成功！**

✅ Telegram Bot 正常工作
📊 錢包別名功能已啟用
🔒 地址隱私保護已開啟

**錢包映射:**
• SM1-SM9: 9個精選錢包
• 每日12點持倉查詢
• 實時開關倉提醒

系統準備就緒！"""
        
        success = await bot.send_message(test_message)
        
        if success:
            print("✅ Telegram 測試成功！")
        else:
            print("❌ Telegram 測試失敗")
            
        await bot.stop()
        return success
        
    except Exception as e:
        print(f"❌ Telegram 測試失敗: {e}")
        return False

async def test_position_with_telegram():
    """測試持倉查詢並發送到 Telegram"""
    print("📊 測試持倉查詢並發送到 Telegram...")
    
    try:
        # 查詢持倉
        async with PositionTracker(Config.WALLET_ADDRESSES) as tracker:
            all_positions = await tracker.get_all_positions()
            message = tracker.format_positions_message(all_positions)
        
        # 發送到 Telegram
        bot = TelegramBot(
            bot_token=Config.TELEGRAM_BOT_TOKEN,
            chat_id=Config.TELEGRAM_CHAT_ID
        )
        
        await bot.start()
        success = await bot.send_message(message)
        await bot.stop()
        
        if success:
            print("✅ 持倉查詢和發送成功！")
        else:
            print("❌ 持倉發送失敗")
            
        return success
        
    except Exception as e:
        print(f"❌ 持倉測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("🚀 簡化版系統測試")
    print("=" * 40)
    
    # 驗證配置
    try:
        Config.validate()
        print("✅ 配置驗證通過")
    except Exception as e:
        print(f"❌ 配置驗證失敗: {e}")
        return
    
    print(f"📊 監聽錢包數量: {len(Config.WALLET_ADDRESSES)}")
    print(f"🔒 錢包別名: SM1-SM{len(Config.WALLET_ADDRESSES)}")
    
    # 測試 Telegram
    telegram_ok = await test_telegram()
    
    if telegram_ok:
        # 測試持倉查詢
        position_ok = await test_position_with_telegram()
        
        if position_ok:
            print("\n🎉 所有測試通過！")
            print("你可以運行 'python main.py' 啟動完整系統")
        else:
            print("\n⚠️ 持倉測試失敗")
    else:
        print("\n❌ Telegram 測試失敗，請檢查配置")

if __name__ == "__main__":
    asyncio.run(main())
