"""
系統設置腳本
幫助用戶快速配置系統
"""
import os
import sys
from pathlib import Path

def create_env_file():
    """創建 .env 文件"""
    env_path = Path('.env')
    
    if env_path.exists():
        response = input("⚠️ .env 文件已存在，是否覆蓋？(y/N): ")
        if response.lower() != 'y':
            print("跳過 .env 文件創建")
            return
            
    print("🔧 配置 Telegram Bot 設置...")
    
    # 收集 Telegram 配置
    telegram_api_id = input("請輸入 Telegram API ID: ").strip()
    telegram_api_hash = input("請輸入 Telegram API Hash: ").strip()
    telegram_bot_token = input("請輸入 Telegram Bot Token: ").strip()
    telegram_chat_id = input("請輸入 Telegram Chat ID (負數): ").strip()
    
    print("\n🔧 配置錢包地址...")
    print("請輸入要監聽的錢包地址（用逗號分隔）:")
    wallet_addresses = input("錢包地址: ").strip()
    
    # 創建 .env 文件內容
    env_content = f"""# Telegram Bot 配置
TELEGRAM_API_ID={telegram_api_id}
TELEGRAM_API_HASH={telegram_api_hash}
TELEGRAM_BOT_TOKEN={telegram_bot_token}
TELEGRAM_CHAT_ID={telegram_chat_id}

# Hyperliquid 配置
HYPERLIQUID_API_KEY=
HYPERLIQUID_API_SECRET=
WEBSOCKET_URL=wss://api.hyperliquid.xyz/ws

# 監聽配置
WALLET_ADDRESSES={wallet_addresses}
RECONNECT_DELAY=5
MAX_RECONNECT_ATTEMPTS=10

# 日誌配置
LOG_LEVEL=INFO
LOG_FILE=hyperliquid_tracker.log
"""
    
    # 寫入文件
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
        
    print("✅ .env 文件創建成功！")

def install_dependencies():
    """安裝依賴"""
    print("📦 安裝 Python 依賴...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依賴安裝成功！")
        else:
            print(f"❌ 依賴安裝失敗: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 安裝依賴時發生錯誤: {e}")

def check_python_version():
    """檢查 Python 版本"""
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 版本過低: {version.major}.{version.minor}")
        print("請安裝 Python 3.8 或更高版本")
        return False
    else:
        print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
        return True

def create_systemd_service():
    """創建 systemd 服務文件（Linux）"""
    if os.name != 'posix':
        print("⚠️ systemd 服務僅適用於 Linux 系統")
        return
        
    current_dir = os.getcwd()
    user = os.getenv('USER', 'ubuntu')
    
    service_content = f"""[Unit]
Description=Hyperliquid Wallet Tracker
After=network.target

[Service]
Type=simple
User={user}
WorkingDirectory={current_dir}
Environment=PATH=/usr/bin:/usr/local/bin
ExecStart={sys.executable} main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_path = '/tmp/hyperliquid-tracker.service'
    
    with open(service_path, 'w') as f:
        f.write(service_content)
        
    print(f"✅ systemd 服務文件已創建: {service_path}")
    print("請執行以下命令安裝服務:")
    print(f"sudo cp {service_path} /etc/systemd/system/")
    print("sudo systemctl daemon-reload")
    print("sudo systemctl enable hyperliquid-tracker")
    print("sudo systemctl start hyperliquid-tracker")

def show_telegram_setup_guide():
    """顯示 Telegram 設置指南"""
    print("""
📱 Telegram 設置指南:

1. 創建 Bot:
   - 找到 @BotFather
   - 發送 /newbot
   - 設置 Bot 名稱和用戶名
   - 獲取 Bot Token

2. 獲取 API 憑證:
   - 前往 https://my.telegram.org/apps
   - 登錄並創建應用
   - 獲取 API ID 和 API Hash

3. 獲取群組 Chat ID:
   - 將 Bot 加入群組
   - 設置為管理員
   - 在群組發送訊息
   - 訪問: https://api.telegram.org/bot<TOKEN>/getUpdates
   - 找到 chat.id (負數)

4. 測試 Bot:
   - 運行: python test_system.py
""")

def main():
    """主設置函數"""
    print("🚀 Hyperliquid 錢包監聽系統設置向導")
    print("=" * 50)
    
    # 檢查 Python 版本
    if not check_python_version():
        return
        
    while True:
        print("\n請選擇操作:")
        print("1. 安裝依賴")
        print("2. 創建配置文件 (.env)")
        print("3. 查看 Telegram 設置指南")
        print("4. 創建 systemd 服務 (Linux)")
        print("5. 運行系統測試")
        print("6. 啟動主程序")
        print("0. 退出")
        
        choice = input("\n請輸入選項 (0-6): ").strip()
        
        if choice == '1':
            install_dependencies()
        elif choice == '2':
            create_env_file()
        elif choice == '3':
            show_telegram_setup_guide()
        elif choice == '4':
            create_systemd_service()
        elif choice == '5':
            print("運行測試...")
            os.system(f"{sys.executable} test_system.py")
        elif choice == '6':
            print("啟動主程序...")
            os.system(f"{sys.executable} main.py")
        elif choice == '0':
            print("👋 再見！")
            break
        else:
            print("❌ 無效選項，請重新選擇")

if __name__ == "__main__":
    main()
