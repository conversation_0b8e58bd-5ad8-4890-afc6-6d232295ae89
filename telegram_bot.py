"""
Telegram Bot 功能
用於發送格式化的交易訊息到指定群組
"""
import asyncio
from telethon import TelegramClient, events
from telethon.errors import FloodWaitError, ChatWriteForbiddenError
from typing import Dict, Any
from loguru import logger
import traceback

class TelegramBot:
    def __init__(self, bot_token: str, chat_id: int):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.client = None
        self.is_connected = False
        
    async def start(self):
        """啟動 Telegram Bot"""
        try:
            logger.info("正在啟動 Telegram Bot...")

            # 使用 requests 發送 HTTP 請求，不需要 Telethon
            import requests

            # 測試 Bot Token 是否有效
            test_url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
            response = requests.get(test_url)

            if response.status_code == 200:
                bot_info = response.json()
                if bot_info.get('ok'):
                    self.is_connected = True
                    logger.success(f"Telegram Bot 啟動成功: @{bot_info['result']['username']}")

                    # 測試發送訊息
                    test_result = await self.send_message("🚀 Hyperliquid 錢包監聽系統已啟動！")
                    if not test_result:
                        logger.warning("測試消息發送失敗，請檢查 TELEGRAM_CHAT_ID 是否正確")
                else:
                    raise Exception(f"Bot Token 無效: {bot_info}")
            else:
                raise Exception(f"無法連接到 Telegram API: HTTP {response.status_code}")

        except Exception as e:
            logger.error(f"Telegram Bot 啟動失敗: {e}")
            logger.error(traceback.format_exc())
            raise
            
    async def send_message(self, message: str, parse_mode: str = 'Markdown'):
        """發送訊息到指定群組"""
        if not self.is_connected:
            logger.error("Telegram Bot 未連接")
            return False

        try:
            import requests

            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }

            response = requests.post(url, data=data)

            if response.status_code == 200:
                result = response.json()
                if result.get('ok'):
                    logger.debug(f"訊息發送成功到群組 {self.chat_id}")
                    return True
                else:
                    logger.error(f"發送訊息失敗: {result}")
                    return False
            else:
                # 獲取詳細錯誤信息
                try:
                    error_result = response.json()
                    logger.error(f"HTTP 請求失敗: {response.status_code}")
                    logger.error(f"錯誤詳情: {error_result}")
                    logger.error(f"Chat ID: {self.chat_id}")
                    logger.error(f"消息長度: {len(message)}")
                except:
                    logger.error(f"HTTP 請求失敗: {response.status_code}")
                    logger.error(f"響應內容: {response.text}")
                return False

        except Exception as e:
            logger.error(f"發送訊息失敗: {e}")
            logger.error(traceback.format_exc())
            return False
            
    async def send_trade_alert(self, trade_info: Dict[str, Any]):
        """發送交易提醒"""
        try:
            # 格式化訊息
            message = self.format_trade_message(trade_info)
            
            # 發送訊息
            success = await self.send_message(message)
            
            if success:
                logger.info(f"交易提醒發送成功: {trade_info['wallet'][:10]}... {trade_info['symbol']} {trade_info['action']}")
            else:
                logger.error(f"交易提醒發送失敗: {trade_info}")
                
            return success
            
        except Exception as e:
            logger.error(f"發送交易提醒時發生錯誤: {e}")
            return False
            
    def format_trade_message(self, trade_info: Dict[str, Any]) -> str:
        """格式化交易訊息"""
        try:
            # 根據動作類型選擇表情符號
            action_emoji = {
                'OPEN': '🟢',
                'CLOSE': '🔴',
                'ADD': '🟡'
            }
            
            # 根據方向選擇表情符號
            side_emoji = {
                'LONG': '📈',
                'SHORT': '📉',
                'BUY': '📈',
                'SELL': '📉'
            }
            
            emoji = action_emoji.get(trade_info['action'], '⚪')
            direction_emoji = side_emoji.get(trade_info['side'], '📊')
            
            # 縮短錢包地址顯示
            wallet_short = f"{trade_info['wallet'][:6]}...{trade_info['wallet'][-4:]}"
            
            # 格式化數量和價格
            size = float(trade_info['size'])
            price = float(trade_info['price'])
            
            message = f"""
{emoji} **{trade_info['action']} 訊號**

💰 錢包: `{wallet_short}`
📊 幣種: **{trade_info['symbol']}**
{direction_emoji} 方向: **{trade_info['side']}**
💎 數量: **{size:,.4f}**
💵 價格: **${price:,.4f}**
⏰ 時間: `{trade_info['timestamp']}`

🔗 [查看 Hyperliquid](https://app.hyperliquid.xyz/trade/{trade_info['symbol']})
            """.strip()
            
            return message
            
        except Exception as e:
            logger.error(f"格式化訊息失敗: {e}")
            # 返回簡化版本
            return f"🔔 {trade_info['action']}: {trade_info['symbol']} {trade_info['side']} {trade_info['size']} @ ${trade_info['price']}"
            
    async def send_system_message(self, message: str, level: str = "INFO"):
        """發送系統訊息"""
        level_emoji = {
            "INFO": "ℹ️",
            "WARNING": "⚠️", 
            "ERROR": "❌",
            "SUCCESS": "✅"
        }
        
        emoji = level_emoji.get(level, "ℹ️")
        formatted_message = f"{emoji} **系統訊息**\n\n{message}"
        
        return await self.send_message(formatted_message)
        
    async def send_statistics(self, stats: Dict[str, Any]):
        """發送統計訊息"""
        try:
            message = f"""
📊 **今日統計**

🔢 總交易數: **{stats.get('total_trades', 0)}**
🟢 開倉次數: **{stats.get('open_trades', 0)}**
🔴 平倉次數: **{stats.get('close_trades', 0)}**
💰 活躍錢包: **{stats.get('active_wallets', 0)}**
⏰ 更新時間: `{stats.get('update_time', 'N/A')}`
            """.strip()
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"發送統計訊息失敗: {e}")
            return False
            
    async def stop(self):
        """停止 Telegram Bot"""
        try:
            if self.is_connected:
                await self.send_message("🛑 Hyperliquid 錢包監聽系統已停止")
                self.is_connected = False
                logger.info("Telegram Bot 已停止")
        except Exception as e:
            logger.error(f"停止 Telegram Bot 時發生錯誤: {e}")

    def is_ready(self) -> bool:
        """檢查 Bot 是否準備就緒"""
        return self.is_connected
