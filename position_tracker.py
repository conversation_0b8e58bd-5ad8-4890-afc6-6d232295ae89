"""
持倉追蹤器
用於查詢和追蹤錢包的當前持倉
"""
import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger
import traceback

class PositionTracker:
    def __init__(self, wallet_addresses: List[str]):
        self.wallet_addresses = [addr.lower() for addr in wallet_addresses]
        self.api_base = "https://api.hyperliquid.xyz"
        self.session = None
        self.known_positions = {}  # 存儲已知的持倉，用於過濾
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    async def get_user_state(self, address: str) -> Optional[Dict]:
        """獲取用戶狀態信息"""
        try:
            payload = {
                "type": "clearinghouseState",
                "user": address
            }
            
            async with self.session.post(f"{self.api_base}/info", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logger.warning(f"獲取用戶狀態失敗 {address}: HTTP {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"獲取用戶狀態時發生錯誤 {address}: {e}")
            return None
            
    async def get_user_fills(self, address: str, start_time: Optional[int] = None) -> List[Dict]:
        """獲取用戶的填單記錄"""
        try:
            payload = {
                "type": "userFills",
                "user": address
            }
            
            if start_time:
                payload["startTime"] = start_time
                
            async with self.session.post(f"{self.api_base}/info", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return data if isinstance(data, list) else []
                else:
                    logger.warning(f"獲取填單記錄失敗 {address}: HTTP {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"獲取填單記錄時發生錯誤 {address}: {e}")
            return []
            
    def parse_positions(self, user_state: Dict) -> List[Dict]:
        """解析持倉信息"""
        positions = []
        
        try:
            if not user_state or 'assetPositions' not in user_state:
                return positions
                
            for asset_pos in user_state['assetPositions']:
                position = asset_pos.get('position', {})
                
                if not position:
                    continue
                    
                # 檢查是否有持倉
                size = float(position.get('szi', '0'))
                if abs(size) < 0.0001:  # 忽略極小的持倉
                    continue
                    
                pos_info = {
                    'coin': position.get('coin', 'UNKNOWN'),
                    'size': size,
                    'side': 'LONG' if size > 0 else 'SHORT',
                    'entry_px': float(position.get('entryPx', '0')),
                    'unrealized_pnl': float(position.get('unrealizedPnl', '0')),
                    'margin_used': float(position.get('marginUsed', '0')),
                    'max_leverage': float(position.get('maxLeverage', '1')),
                    'timestamp': datetime.now().isoformat()
                }
                
                positions.append(pos_info)
                
        except Exception as e:
            logger.error(f"解析持倉信息時發生錯誤: {e}")
            
        return positions
        
    async def get_all_positions(self) -> Dict[str, List[Dict]]:
        """獲取所有錢包的當前持倉"""
        all_positions = {}
        
        for address in self.wallet_addresses:
            try:
                logger.info(f"查詢錢包持倉: {address[:10]}...")
                user_state = await self.get_user_state(address)
                
                if user_state:
                    positions = self.parse_positions(user_state)
                    if positions:
                        all_positions[address] = positions
                        logger.info(f"錢包 {address[:10]}... 有 {len(positions)} 個持倉")
                    else:
                        logger.info(f"錢包 {address[:10]}... 無持倉")
                else:
                    logger.warning(f"無法獲取錢包 {address[:10]}... 的狀態")
                    
                # 避免請求過於頻繁
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"查詢錢包 {address} 持倉時發生錯誤: {e}")
                
        return all_positions
        
    def format_positions_message(self, all_positions: Dict[str, List[Dict]]) -> str:
        """格式化持倉訊息"""
        if not all_positions:
            return "📊 **當前持倉查詢**\n\n暫無活躍持倉"

        message_parts = ["📊 **當前持倉查詢**\n"]

        total_positions = 0
        for address, positions in all_positions.items():
            if not positions:
                continue

            # 使用錢包別名而不是真實地址
            from config import Config
            wallet_alias = Config.get_wallet_alias(address)
            message_parts.append(f"\n👤 **錢包**: **{wallet_alias}**")

            for pos in positions:
                side_emoji = "📈" if pos['side'] == 'LONG' else "📉"
                pnl_emoji = "🟢" if pos['unrealized_pnl'] >= 0 else "🔴"

                message_parts.append(
                    f"  {side_emoji} **{pos['coin']}** {pos['side']}\n"
                    f"    💎 數量: {abs(pos['size']):.4f}\n"
                    f"    💵 入場價: ${pos['entry_px']:.4f}\n"
                    f"    {pnl_emoji} 未實現盈虧: ${pos['unrealized_pnl']:.2f}"
                )
                total_positions += 1

        message_parts.append(f"\n📈 **總計**: {total_positions} 個持倉")
        message_parts.append(f"⏰ **查詢時間**: `{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}`")

        return "\n".join(message_parts)
        
    def is_new_position(self, address: str, position: Dict) -> bool:
        """檢查是否為新持倉（相對於上次查詢）"""
        key = f"{address}_{position['coin']}"
        
        if key not in self.known_positions:
            # 第一次見到這個持倉
            self.known_positions[key] = position
            return True
            
        old_pos = self.known_positions[key]
        
        # 檢查持倉是否有顯著變化
        size_changed = abs(position['size'] - old_pos['size']) > 0.001
        side_changed = position['side'] != old_pos['side']
        
        if size_changed or side_changed:
            self.known_positions[key] = position
            return True
            
        return False
        
    async def check_new_positions(self) -> List[Dict]:
        """檢查新的持倉變化"""
        all_positions = await self.get_all_positions()
        new_positions = []
        
        for address, positions in all_positions.items():
            for position in positions:
                if self.is_new_position(address, position):
                    position['wallet'] = address
                    new_positions.append(position)
                    
        return new_positions
        
    def save_positions_snapshot(self, all_positions: Dict[str, List[Dict]]):
        """保存持倉快照到文件"""
        try:
            snapshot = {
                'timestamp': datetime.now().isoformat(),
                'positions': all_positions
            }
            
            filename = f"positions_snapshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(snapshot, f, indent=2, ensure_ascii=False)
                
            logger.info(f"持倉快照已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存持倉快照失敗: {e}")

async def test_position_tracker():
    """測試持倉追蹤器"""
    from config import Config
    
    print("🧪 測試持倉追蹤器...")
    
    async with PositionTracker(Config.WALLET_ADDRESSES) as tracker:
        # 獲取所有持倉
        all_positions = await tracker.get_all_positions()
        
        # 格式化訊息
        message = tracker.format_positions_message(all_positions)
        print("\n" + "="*50)
        print(message)
        print("="*50)
        
        # 保存快照
        tracker.save_positions_snapshot(all_positions)

if __name__ == "__main__":
    asyncio.run(test_position_tracker())
