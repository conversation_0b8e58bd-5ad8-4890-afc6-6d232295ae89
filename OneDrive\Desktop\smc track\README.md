# Hyperliquid 錢包監聽系統

這是一個用於監聽 Hyperliquid 平台上指定錢包交易活動並自動轉發到 Telegram 群組的系統。

## 功能特色

- 🔍 **實時監聽**: 監聽多個精選錢包的開關倉活動
- 🚫 **智能過濾**: 自動過濾加倉訊號，只顯示開倉和平倉
- 📱 **Telegram 整合**: 自動格式化並發送到指定 Telegram 群組
- 🔄 **穩定重連**: 自動重連機制確保服務穩定運行
- 📊 **詳細日誌**: 完整的操作日誌記錄

## 快速開始

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

### 2. 配置環境變數

複製 `.env.example` 為 `.env` 並填入你的配置：

```bash
cp .env.example .env
```

### 3. 獲取必要的 API 金鑰

#### Telegram Bot 設置
1. 找 @BotFather 創建新的 Bot
2. 獲取 Bot Token
3. 將 Bot 加入你的群組並設為管理員
4. 獲取群組 Chat ID

#### Telegram API 設置
1. 前往 https://my.telegram.org/apps
2. 創建新應用獲取 API ID 和 API Hash

### 4. 配置錢包地址

在 `.env` 文件中設置要監聽的錢包地址：

```
WALLET_ADDRESSES=0x123...,0x456...,0x789...
```

### 5. 運行系統

```bash
python main.py
```

## 項目結構

```
hyperliquid_tracker/
├── main.py              # 主程序入口
├── config.py            # 配置管理
├── hyperliquid_client.py # Hyperliquid WebSocket 客戶端
├── telegram_bot.py      # Telegram Bot 功能
├── message_formatter.py # 訊息格式化
├── requirements.txt     # 依賴列表
├── .env.example        # 環境變數模板
└── README.md           # 說明文件
```

## 部署建議

### 本地運行
適合測試和小規模使用

### 雲端部署
推薦使用以下平台：
- AWS EC2
- Google Cloud Platform
- DigitalOcean
- Railway
- Heroku

## 注意事項

1. **API 限制**: 注意 Hyperliquid 和 Telegram 的 API 頻率限制
2. **網路穩定**: 確保網路連接穩定，系統會自動重連
3. **安全性**: 妥善保管 API 金鑰，不要提交到版本控制
4. **監控**: 建議設置監控和告警機制

## 故障排除

### 常見問題
1. **連接失敗**: 檢查網路連接和 API 金鑰
2. **訊息發送失敗**: 確認 Bot 在群組中且有發送權限
3. **重複訊息**: 檢查是否有多個實例在運行

### 日誌查看
系統會生成詳細日誌，可以通過以下方式查看：

```bash
tail -f hyperliquid_tracker.log
```

## 支援

如有問題請查看日誌文件或聯繫開發者。
