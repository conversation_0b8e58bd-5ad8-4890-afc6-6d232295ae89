@echo off
echo 🚀 啟動 Hyperliquid 錢包監聽系統...
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安裝或不在 PATH 中
    echo 請安裝 Python 3.8+ 並添加到 PATH
    pause
    exit /b 1
)

REM 檢查是否存在 .env 文件
if not exist .env (
    echo ⚠️ 未找到 .env 配置文件
    echo 請先運行 setup.py 進行配置
    echo.
    set /p choice="是否現在運行設置向導？(Y/N): "
    if /i "%choice%"=="Y" (
        python setup.py
    ) else (
        echo 請手動創建 .env 文件後重新運行
        pause
        exit /b 1
    )
)

REM 檢查依賴是否安裝
echo 📦 檢查依賴...
python -c "import websockets, telethon, loguru" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 依賴未安裝，正在安裝...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依賴安裝失敗
        pause
        exit /b 1
    )
)

REM 運行測試（可選）
set /p test_choice="是否運行系統測試？(Y/N): "
if /i "%test_choice%"=="Y" (
    echo 🧪 運行系統測試...
    python test_system.py
    if errorlevel 1 (
        echo ⚠️ 測試失敗，但仍可嘗試啟動主程序
        pause
    )
)

REM 啟動主程序
echo.
echo ✅ 啟動主程序...
python main.py

REM 如果程序異常退出
if errorlevel 1 (
    echo.
    echo ❌ 程序異常退出
    echo 請檢查日誌文件: hyperliquid_tracker.log
)

pause
