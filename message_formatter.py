"""
訊息格式化和過濾器
用於處理和格式化交易訊息
"""
from typing import Dict, Any, Optional, List
from datetime import datetime
from loguru import logger
import re

class MessageFormatter:
    def __init__(self):
        # 定義動作類型映射
        self.action_mapping = {
            'open long': 'OPEN',
            'open short': 'OPEN', 
            'close long': 'CLOSE',
            'close short': 'CLOSE',
            'buy': 'OPEN',
            'sell': 'CLOSE'
        }
        
        # 定義方向映射
        self.side_mapping = {
            'open long': 'LONG',
            'open short': 'SHORT',
            'close long': 'LONG',
            'close short': 'SHORT',
            'buy': 'LONG',
            'sell': 'SHORT'
        }
        
        # 過濾關鍵詞 (用於識別加倉)
        self.add_position_keywords = [
            'add', 'increase', 'scale', 'pyramid', 'dca'
        ]
        
    def should_filter_message(self, trade_info: Dict[str, Any]) -> bool:
        """
        判斷是否應該過濾此訊息
        返回 True 表示應該過濾（不發送）
        """
        try:
            action = trade_info.get('action', '').lower()
            
            # 過濾加倉訊號
            if action == 'ADD':
                logger.debug(f"過濾加倉訊號: {trade_info}")
                return True
                
            # 檢查是否包含加倉關鍵詞
            raw_data = trade_info.get('raw_data', {})
            direction = raw_data.get('dir', '').lower()
            
            for keyword in self.add_position_keywords:
                if keyword in direction:
                    logger.debug(f"檢測到加倉關鍵詞 '{keyword}': {trade_info}")
                    return True
                    
            # 檢查交易大小是否異常小（可能是測試交易）
            try:
                size = float(trade_info.get('size', 0))
                if size < 0.001:  # 小於 0.001 的交易可能是測試
                    logger.debug(f"過濾小額交易: {trade_info}")
                    return True
            except (ValueError, TypeError):
                pass
                
            return False
            
        except Exception as e:
            logger.error(f"過濾訊息時發生錯誤: {e}")
            return False
            
    def normalize_trade_info(self, trade_info: Dict[str, Any]) -> Dict[str, Any]:
        """標準化交易信息"""
        try:
            normalized = trade_info.copy()
            
            # 標準化動作類型
            action = trade_info.get('action', '').upper()
            if action not in ['OPEN', 'CLOSE']:
                # 嘗試從原始數據推斷
                raw_data = trade_info.get('raw_data', {})
                direction = raw_data.get('dir', '').lower()
                
                normalized['action'] = self.action_mapping.get(direction, 'UNKNOWN')
                normalized['side'] = self.side_mapping.get(direction, 'UNKNOWN')
                
            # 標準化錢包地址格式
            wallet = trade_info.get('wallet', '')
            if wallet and not wallet.startswith('0x'):
                normalized['wallet'] = f"0x{wallet}"
                
            # 標準化幣種名稱
            symbol = trade_info.get('symbol', '').upper()
            normalized['symbol'] = symbol
            
            # 標準化數量和價格格式
            try:
                size = float(trade_info.get('size', 0))
                price = float(trade_info.get('price', 0))
                normalized['size'] = f"{size:.6f}".rstrip('0').rstrip('.')
                normalized['price'] = f"{price:.6f}".rstrip('0').rstrip('.')
            except (ValueError, TypeError):
                pass
                
            # 確保時間戳格式正確
            if 'timestamp' not in normalized or not normalized['timestamp']:
                normalized['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
            return normalized
            
        except Exception as e:
            logger.error(f"標準化交易信息失敗: {e}")
            return trade_info
            
    def format_for_telegram(self, trade_info: Dict[str, Any]) -> str:
        """為 Telegram 格式化訊息"""
        try:
            # 先標準化數據
            normalized = self.normalize_trade_info(trade_info)

            # 選擇表情符號
            action_emoji = {
                'OPEN': '🟢 開倉',
                'CLOSE': '🔴 平倉',
                'ADD': '🟡 加倉'
            }

            side_emoji = {
                'LONG': '📈 做多',
                'SHORT': '📉 做空'
            }

            action_text = action_emoji.get(normalized['action'], f"⚪ {normalized['action']}")
            side_text = side_emoji.get(normalized['side'], f"📊 {normalized['side']}")

            # 使用錢包別名而不是真實地址
            from config import Config
            wallet = normalized['wallet']
            wallet_alias = Config.get_wallet_alias(wallet)

            # 計算交易價值（如果有價格和數量）
            try:
                size = float(normalized['size'])
                price = float(normalized['price'])
                value = size * price
                value_text = f"\n💰 價值: **${value:,.2f}**" if value > 0 else ""
            except:
                value_text = ""

            # 構建訊息
            message = f"""
🚨 **{action_text}**

👤 錢包: **{wallet_alias}**
🪙 幣種: **{normalized['symbol']}**
{side_text}
📊 數量: **{normalized['size']}**
💵 價格: **${normalized['price']}**{value_text}
⏰ 時間: `{normalized['timestamp']}`

🔗 [查看詳情](https://app.hyperliquid.xyz/trade/{normalized['symbol']})
            """.strip()

            return message

        except Exception as e:
            logger.error(f"格式化 Telegram 訊息失敗: {e}")
            # 返回簡化版本
            return f"🔔 {trade_info.get('action', 'TRADE')}: {trade_info.get('symbol', 'UNKNOWN')} {trade_info.get('side', '')} {trade_info.get('size', '')} @ ${trade_info.get('price', '')}"
            
    def create_summary_message(self, trades: List[Dict[str, Any]], timeframe: str = "1小時") -> str:
        """創建交易摘要訊息"""
        try:
            if not trades:
                return f"📊 **{timeframe}內無交易活動**"
                
            # 統計數據
            total_trades = len(trades)
            open_trades = len([t for t in trades if t.get('action') == 'OPEN'])
            close_trades = len([t for t in trades if t.get('action') == 'CLOSE'])
            
            # 活躍錢包
            active_wallets = len(set(t.get('wallet', '') for t in trades))
            
            # 熱門幣種
            symbols = {}
            for trade in trades:
                symbol = trade.get('symbol', '')
                symbols[symbol] = symbols.get(symbol, 0) + 1
                
            top_symbols = sorted(symbols.items(), key=lambda x: x[1], reverse=True)[:3]
            
            message = f"""
📊 **{timeframe}交易摘要**

🔢 總交易數: **{total_trades}**
🟢 開倉: **{open_trades}** | 🔴 平倉: **{close_trades}**
👥 活躍錢包: **{active_wallets}**

🔥 **熱門幣種:**
            """
            
            for symbol, count in top_symbols:
                message += f"\n• {symbol}: {count} 次"
                
            message += f"\n\n⏰ 更新時間: `{datetime.now().strftime('%H:%M:%S')}`"
            
            return message
            
        except Exception as e:
            logger.error(f"創建摘要訊息失敗: {e}")
            return f"📊 **{timeframe}交易摘要** - 處理錯誤"
            
    def validate_trade_info(self, trade_info: Dict[str, Any]) -> bool:
        """驗證交易信息的完整性"""
        required_fields = ['wallet', 'symbol', 'action', 'side', 'size', 'price']
        
        for field in required_fields:
            if field not in trade_info or not trade_info[field]:
                logger.warning(f"交易信息缺少必要字段: {field}")
                return False
                
        return True
